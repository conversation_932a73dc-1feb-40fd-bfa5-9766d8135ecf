{"version": 1, "defects": {"Thorne\\Wallet\\Tests\\Unit\\Support\\Traits\\SerializesAttributesUnitTest::it_transforms_carbon_dates_correctly": 3, "Thorne\\Wallet\\Tests\\Unit\\Domain\\ValueObjects\\AccountNumberTest::it_should_generate_valid_account_number_for_supported_currency": 4, "Thorne\\Wallet\\Tests\\Unit\\Domain\\ValueObjects\\AccountNumberTest::it_should_throw_exception_when_generating_for_unsupported_currency": 4, "Thorne\\Wallet\\Tests\\Unit\\Domain\\ValueObjects\\AccountNumberTest::it_should_create_account_number_from_valid_string": 4, "Thorne\\Wallet\\Tests\\Unit\\Domain\\ValueObjects\\AccountNumberTest::it_should_create_account_number_from_formatted_string": 4, "Thorne\\Wallet\\Tests\\Unit\\Domain\\ValueObjects\\AccountNumberTest::it_should_throw_exception_for_invalid_length": 4, "Thorne\\Wallet\\Tests\\Unit\\Domain\\ValueObjects\\AccountNumberTest::it_should_throw_exception_for_non_digit_characters": 4, "Thorne\\Wallet\\Tests\\Unit\\Domain\\ValueObjects\\AccountNumberTest::it_should_throw_exception_for_invalid_checksum": 4, "Thorne\\Wallet\\Tests\\Unit\\Domain\\ValueObjects\\AccountNumberTest::it_should_return_correct_currency_iso_code": 4, "Thorne\\Wallet\\Tests\\Unit\\Domain\\ValueObjects\\AccountNumberTest::it_should_return_correct_sequence_number": 4, "Thorne\\Wallet\\Tests\\Unit\\Domain\\ValueObjects\\AccountNumberTest::it_should_return_correct_check_digit": 4, "Thorne\\Wallet\\Tests\\Unit\\Domain\\ValueObjects\\AccountNumberTest::it_should_return_formatted_account_number": 4, "Thorne\\Wallet\\Tests\\Unit\\Domain\\ValueObjects\\AccountNumberTest::it_should_return_masked_account_number": 4, "Thorne\\Wallet\\Tests\\Unit\\Domain\\ValueObjects\\AccountNumberTest::it_should_return_currency_object": 4, "Thorne\\Wallet\\Tests\\Unit\\Domain\\ValueObjects\\AccountNumberTest::it_should_throw_exception_for_unknown_currency_iso_code": 4, "Thorne\\Wallet\\Tests\\Unit\\Domain\\ValueObjects\\AccountNumberTest::it_should_return_correct_account_type": 4, "Thorne\\Wallet\\Tests\\Unit\\Domain\\ValueObjects\\AccountNumberTest::it_should_check_if_belongs_to_currency": 4, "Thorne\\Wallet\\Tests\\Unit\\Domain\\ValueObjects\\AccountNumberTest::it_should_return_correct_region_for_european_currency": 4, "Thorne\\Wallet\\Tests\\Unit\\Domain\\ValueObjects\\AccountNumberTest::it_should_generate_sibling_account_number": 4, "Thorne\\Wallet\\Tests\\Unit\\Domain\\ValueObjects\\AccountNumberTest::it_should_validate_account_number_for_specific_currency": 4, "Thorne\\Wallet\\Tests\\Unit\\Domain\\ValueObjects\\AccountNumberTest::it_should_validate_account_number": 4, "Thorne\\Wallet\\Tests\\Unit\\Domain\\ValueObjects\\AccountNumberTest::it_should_check_equality_between_account_numbers": 4, "Thorne\\Wallet\\Tests\\Unit\\Domain\\ValueObjects\\AccountNumberTest::it_should_convert_to_array": 4, "Thorne\\Wallet\\Tests\\Unit\\Domain\\ValueObjects\\AccountNumberTest::it_should_convert_to_string": 4, "Thorne\\Wallet\\Tests\\Unit\\Domain\\ValueObjects\\AccountNumberTest::it_should_be_json_serializable": 4, "Thorne\\Wallet\\Tests\\Unit\\Domain\\ValueObjects\\AccountNumberTest::it_should_generate_different_account_numbers_on_consecutive_calls": 4, "Thorne\\Wallet\\Tests\\Unit\\Domain\\ValueObjects\\AccountNumberTest::it_should_calculate_correct_luhn_checksum": 4, "Thorne\\Wallet\\Tests\\Unit\\Domain\\ValueObjects\\AccountNumberTest::it_should_handle_edge_case_account_numbers": 4}, "times": {"Thorne\\Wallet\\Tests\\Unit\\Support\\Traits\\InteractsWithEventsUnitTest::it_records_single_event_with_metadata": 0.268, "Thorne\\Wallet\\Tests\\Unit\\Support\\Traits\\InteractsWithEventsUnitTest::it_records_multiple_events_in_sequence": 0.019, "Thorne\\Wallet\\Tests\\Unit\\Support\\Traits\\InteractsWithEventsUnitTest::it_releases_events_and_clears_list": 0.021, "Thorne\\Wallet\\Tests\\Unit\\Support\\Traits\\InteractsWithEventsUnitTest::it_clears_recorded_events": 0.019, "Thorne\\Wallet\\Tests\\Unit\\Support\\Traits\\InteractsWithEventsUnitTest::it_returns_first_and_last_recorded_events": 0.027, "Thorne\\Wallet\\Tests\\Unit\\Support\\Traits\\InteractsWithEventsUnitTest::it_dispatches_all_recorded_events": 0.024, "Thorne\\Wallet\\Tests\\Unit\\Support\\Traits\\InteractsWithEventsUnitTest::it_dispatches_single_event_immediately": 0.021, "Thorne\\Wallet\\Tests\\Unit\\Support\\Traits\\InteractsWithEventsUnitTest::it_records_and_dispatches_event_in_single_operation": 0.021, "Thorne\\Wallet\\Tests\\Unit\\Support\\Traits\\InteractsWithEventsUnitTest::it_enables_and_disables_event_recording": 0.023, "Thorne\\Wallet\\Tests\\Unit\\Support\\Traits\\InteractsWithEventsUnitTest::it_executes_callback_without_events_and_restores_state": 0.02, "Thorne\\Wallet\\Tests\\Unit\\Support\\Traits\\InteractsWithEventsUnitTest::it_filters_events_by_type": 0.023, "Thorne\\Wallet\\Tests\\Unit\\Support\\Traits\\InteractsWithEventsUnitTest::it_gets_events_by_type_and_returns_last_event": 0.021, "Thorne\\Wallet\\Tests\\Unit\\Support\\Traits\\InteractsWithEventsUnitTest::it_removes_events_of_specific_type": 0.02, "Thorne\\Wallet\\Tests\\Unit\\Support\\Traits\\InteractsWithEventsUnitTest::it_manages_event_metadata": 0.025, "Thorne\\Wallet\\Tests\\Unit\\Support\\Traits\\InteractsWithEventsUnitTest::it_prepares_event_metadata_with_automatic_fields": 0.02, "Thorne\\Wallet\\Tests\\Unit\\Support\\Traits\\InteractsWithEventsUnitTest::it_provides_event_timeline": 0.02, "Thorne\\Wallet\\Tests\\Unit\\Support\\Traits\\InteractsWithEventsUnitTest::it_provides_events_summary": 0.024, "Thorne\\Wallet\\Tests\\Unit\\Support\\Traits\\InteractsWithEventsUnitTest::it_replays_events_through_callback": 0.02, "Thorne\\Wallet\\Tests\\Unit\\Support\\Traits\\InteractsWithEventsUnitTest::it_copies_events_from_another_source": 0.023, "Thorne\\Wallet\\Tests\\Unit\\Support\\Traits\\InteractsWithEventsUnitTest::it_merges_events_from_another_source": 0.021, "Thorne\\Wallet\\Tests\\Unit\\Support\\Traits\\InteractsWithEventsUnitTest::it_exports_events_as_json": 0.029, "Thorne\\Wallet\\Tests\\Unit\\Support\\Traits\\InteractsWithEventsUnitTest::it_validates_event_integrity": 0.039, "Thorne\\Wallet\\Tests\\Unit\\Support\\Traits\\MakesUuidUnitTest::it_initializes_uuid_when_not_set": 0.029, "Thorne\\Wallet\\Tests\\Unit\\Support\\Traits\\MakesUuidUnitTest::it_does_not_reinitialize_uuid_when_already_set": 0.06, "Thorne\\Wallet\\Tests\\Unit\\Support\\Traits\\MakesUuidUnitTest::it_generates_new_uuid_and_returns_it": 0.028, "Thorne\\Wallet\\Tests\\Unit\\Support\\Traits\\MakesUuidUnitTest::it_ensures_uuid_exists_and_returns_it": 0.023, "Thorne\\Wallet\\Tests\\Unit\\Support\\Traits\\MakesUuidUnitTest::it_checks_if_uuid_exists": 0.024, "Thorne\\Wallet\\Tests\\Unit\\Support\\Traits\\MakesUuidUnitTest::it_sets_valid_uuid_correctly": 0.027, "Thorne\\Wallet\\Tests\\Unit\\Support\\Traits\\MakesUuidUnitTest::it_fails_with_exception_for_invalid_uuid": 0.024, "Thorne\\Wallet\\Tests\\Unit\\Support\\Traits\\MakesUuidUnitTest::it_returns_uuid_object_interface": 0.026, "Thorne\\Wallet\\Tests\\Unit\\Support\\Traits\\MakesUuidUnitTest::it_clears_uuid_correctly": 0.023, "Thorne\\Wallet\\Tests\\Unit\\Support\\Traits\\MakesUuidUnitTest::it_regenerates_uuid_with_new_value": 0.034, "Thorne\\Wallet\\Tests\\Unit\\Support\\Traits\\MakesUuidUnitTest::it_creates_new_uuid_statically": 0.025, "Thorne\\Wallet\\Tests\\Unit\\Support\\Traits\\MakesUuidUnitTest::it_validates_uuid_format_statically": 0.021, "Thorne\\Wallet\\Tests\\Unit\\Support\\Traits\\MakesUuidUnitTest::it_creates_instance_from_uuid_statically": 0.021, "Thorne\\Wallet\\Tests\\Unit\\Support\\Traits\\MakesUuidUnitTest::it_converts_to_string_representation": 0.025, "Thorne\\Wallet\\Tests\\Unit\\Support\\Traits\\MakesUuidUnitTest::it_returns_uuid_string_ensuring_existence": 0.02, "Thorne\\Wallet\\Tests\\Unit\\Support\\Traits\\MakesUuidUnitTest::it_compares_equality_with_other_instances": 0.022, "Thorne\\Wallet\\Tests\\Unit\\Support\\Traits\\MakesUuidUnitTest::it_provides_uuid_for_serialization": 0.02, "Thorne\\Wallet\\Tests\\Unit\\Support\\Traits\\MakesUuidUnitTest::it_sets_uuid_from_serialization_data": 0.02, "Thorne\\Wallet\\Tests\\Unit\\Support\\Traits\\MakesUuidUnitTest::it_handles_serialization_data_without_uuid": 0.023, "Thorne\\Wallet\\Tests\\Unit\\Support\\Traits\\MakesUuidUnitTest::it_compares_uuid_differences": 0.02, "Thorne\\Wallet\\Tests\\Unit\\Support\\Traits\\MakesUuidUnitTest::it_creates_clone_with_new_uuid": 0.02, "Thorne\\Wallet\\Tests\\Unit\\Support\\Traits\\MakesUuidUnitTest::it_creates_clone_with_specific_uuid": 0.022, "Thorne\\Wallet\\Tests\\Unit\\Support\\Traits\\MakesUuidUnitTest::it_returns_short_uuid_with_specified_length": 0.02, "Thorne\\Wallet\\Tests\\Unit\\Support\\Traits\\MakesUuidUnitTest::it_returns_uuid_version": 0.026, "Thorne\\Wallet\\Tests\\Unit\\Support\\Traits\\MakesUuidUnitTest::it_checks_if_uuid_is_nil": 0.023, "Thorne\\Wallet\\Tests\\Unit\\Support\\Traits\\MakesUuidUnitTest::it_returns_hyphenated_uuid_format": 0.02, "Thorne\\Wallet\\Tests\\Unit\\Support\\Traits\\MakesUuidUnitTest::it_returns_compact_uuid_format": 0.023, "Thorne\\Wallet\\Tests\\Unit\\Support\\Traits\\MakesUuidUnitTest::it_returns_uppercase_uuid_format": 0.024, "Thorne\\Wallet\\Tests\\Unit\\Support\\Traits\\MakesUuidUnitTest::it_returns_lowercase_uuid_format": 0.025, "Thorne\\Wallet\\Tests\\Unit\\Support\\Traits\\MakesUuidUnitTest::it_returns_uuid_timestamp_for_time_based_uuids": 0.035, "Thorne\\Wallet\\Tests\\Unit\\Support\\Traits\\MakesUuidUnitTest::it_returns_uuid_node_for_time_based_uuids": 0.022, "Thorne\\Wallet\\Tests\\Unit\\Support\\Traits\\MakesUuidUnitTest::it_validates_uuid_integrity": 0.021, "Thorne\\Wallet\\Tests\\Unit\\Support\\Traits\\MakesUuidUnitTest::it_handles_empty_uuid_string": 0.023, "Thorne\\Wallet\\Tests\\Unit\\Support\\Traits\\MakesUuidUnitTest::it_boots_trait_correctly": 0.021, "Thorne\\Wallet\\Tests\\Unit\\Support\\Traits\\SerializesAttributesUnitTest::it_serializes_all_properties_to_array_by_default": 0.02, "Thorne\\Wallet\\Tests\\Unit\\Support\\Traits\\SerializesAttributesUnitTest::it_converts_to_json_string": 0.023, "Thorne\\Wallet\\Tests\\Unit\\Support\\Traits\\SerializesAttributesUnitTest::it_fails_with_exception_for_invalid_json_encoding": 0.02, "Thorne\\Wallet\\Tests\\Unit\\Support\\Traits\\SerializesAttributesUnitTest::it_creates_instance_from_array_data": 0.025, "Thorne\\Wallet\\Tests\\Unit\\Support\\Traits\\SerializesAttributesUnitTest::it_creates_instance_from_json_string": 0.027, "Thorne\\Wallet\\Tests\\Unit\\Support\\Traits\\SerializesAttributesUnitTest::it_fails_with_exception_for_invalid_json": 0.027, "Thorne\\Wallet\\Tests\\Unit\\Support\\Traits\\SerializesAttributesUnitTest::it_implements_json_serializable_interface": 0.032, "Thorne\\Wallet\\Tests\\Unit\\Support\\Traits\\SerializesAttributesUnitTest::it_excludes_properties_from_serialization": 0.049, "Thorne\\Wallet\\Tests\\Unit\\Support\\Traits\\SerializesAttributesUnitTest::it_adds_properties_to_exclusion_list": 0.027, "Thorne\\Wallet\\Tests\\Unit\\Support\\Traits\\SerializesAttributesUnitTest::it_includes_only_specified_properties": 0.032, "Thorne\\Wallet\\Tests\\Unit\\Support\\Traits\\SerializesAttributesUnitTest::it_adds_properties_to_inclusion_list": 0.029, "Thorne\\Wallet\\Tests\\Unit\\Support\\Traits\\SerializesAttributesUnitTest::it_sets_custom_attribute_transformer": 0.032, "Thorne\\Wallet\\Tests\\Unit\\Support\\Traits\\SerializesAttributesUnitTest::it_sets_attribute_transformer_with_reverse": 0.027, "Thorne\\Wallet\\Tests\\Unit\\Support\\Traits\\SerializesAttributesUnitTest::it_removes_attribute_transformer": 0.028, "Thorne\\Wallet\\Tests\\Unit\\Support\\Traits\\SerializesAttributesUnitTest::it_configures_property_visibility_for_serialization": 0.032, "Thorne\\Wallet\\Tests\\Unit\\Support\\Traits\\SerializesAttributesUnitTest::it_returns_only_specified_attributes": 0.027, "Thorne\\Wallet\\Tests\\Unit\\Support\\Traits\\SerializesAttributesUnitTest::it_returns_all_except_specified_attributes": 0.027, "Thorne\\Wallet\\Tests\\Unit\\Support\\Traits\\SerializesAttributesUnitTest::it_checks_if_attribute_exists": 0.033, "Thorne\\Wallet\\Tests\\Unit\\Support\\Traits\\SerializesAttributesUnitTest::it_checks_if_serialized_data_is_empty": 0.027, "Thorne\\Wallet\\Tests\\Unit\\Support\\Traits\\SerializesAttributesUnitTest::it_provides_serialization_metadata": 0.027, "Thorne\\Wallet\\Tests\\Unit\\Support\\Traits\\SerializesAttributesUnitTest::it_returns_data_with_serialization_metadata": 0.03, "Thorne\\Wallet\\Tests\\Unit\\Support\\Traits\\SerializesAttributesUnitTest::it_converts_to_laravel_collection": 0.028, "Thorne\\Wallet\\Tests\\Unit\\Support\\Traits\\SerializesAttributesUnitTest::it_returns_pretty_formatted_json": 0.031, "Thorne\\Wallet\\Tests\\Unit\\Support\\Traits\\SerializesAttributesUnitTest::it_generates_hash_of_serialized_data": 0.042, "Thorne\\Wallet\\Tests\\Unit\\Support\\Traits\\SerializesAttributesUnitTest::it_generates_fingerprint_of_serialized_data": 0.101, "Thorne\\Wallet\\Tests\\Unit\\Support\\Traits\\SerializesAttributesUnitTest::it_transforms_carbon_dates_correctly": 0.063, "Thorne\\Wallet\\Tests\\Unit\\Support\\Traits\\SerializesAttributesUnitTest::it_transforms_datetime_objects_correctly": 0.035, "Thorne\\Wallet\\Tests\\Unit\\Support\\Traits\\SerializesAttributesUnitTest::it_transforms_json_serializable_objects_correctly": 0.024, "Thorne\\Wallet\\Tests\\Unit\\Support\\Traits\\SerializesAttributesUnitTest::it_transforms_collection_objects_correctly": 0.026, "Thorne\\Wallet\\Tests\\Unit\\Support\\Traits\\SerializesAttributesUnitTest::it_transforms_nested_arrays_correctly": 0.024, "Thorne\\Wallet\\Tests\\Unit\\Support\\Traits\\SerializesAttributesUnitTest::it_transforms_objects_with_to_array_method": 0.026, "Thorne\\Wallet\\Tests\\Unit\\Support\\Traits\\SerializesAttributesUnitTest::it_transforms_objects_with_to_string_method": 0.037, "Thorne\\Wallet\\Tests\\Unit\\Support\\Traits\\SerializesAttributesUnitTest::it_transforms_generic_objects_with_public_properties": 0.023, "Thorne\\Wallet\\Tests\\Unit\\Support\\Traits\\SerializesAttributesUnitTest::it_reverse_transforms_datetime_strings": 0.025, "Thorne\\Wallet\\Tests\\Unit\\Support\\Traits\\SerializesAttributesUnitTest::it_reverse_transforms_nested_arrays": 0.023, "Thorne\\Wallet\\Tests\\Unit\\Support\\Traits\\SerializesAttributesUnitTest::it_detects_datetime_like_strings": 0.023, "Thorne\\Wallet\\Tests\\Unit\\Support\\Traits\\SerializesAttributesUnitTest::it_handles_invalid_datetime_strings_gracefully": 0.038, "Thorne\\FastOrder\\Tests\\Feature\\WebhookSecurityTest::test_webhook_security": 0.245, "Thorne\\FastOrder\\Tests\\Unit\\FastOrderServiceFullTest::test_cache_driver_does_not_support_tags": 0.051, "Thorne\\FastOrder\\Tests\\Unit\\FastOrderServiceFullTest::test_lock_timeout_throws_exception": 0.026, "Thorne\\FastOrder\\Tests\\Unit\\FastOrderServiceFullTest::test_global_limit_exceeded_throws_exception": 0.022, "Thorne\\FastOrder\\Tests\\Unit\\FastOrderServiceFullTest::test_customer_limit_exceeded_throws_exception": 0.025, "Thorne\\FastOrder\\Tests\\Unit\\FastOrderServiceFullTest::test_semaphore_increments_and_decrements": 0.031, "Thorne\\FastOrder\\Tests\\Unit\\FastOrderServiceFullTest::test_get_safe_message_returns_public_for_non_admin": 0.026, "Thorne\\FastOrder\\Tests\\Unit\\FastOrderServiceFullTest::test_get_safe_message_returns_internal_and_public_for_admin": 0.022, "Thorne\\FastOrder\\Tests\\Unit\\FastOrderServiceFullTest::test_get_priority_returns_correct_label": 0.025, "Thorne\\FastOrder\\Tests\\Unit\\FastOrderServiceFullTest::test_validate_hmac_throws_exception_for_invalid_signature": 0.023, "Thorne\\FastOrder\\Tests\\Unit\\FastOrderServiceFullTest::test_validate_hmac_succeeds_with_valid_signature": 0.024, "Thorne\\FastOrder\\Tests\\Unit\\FastOrderServiceFullTest::test_sign_price_token_generates_valid_token": 0.023, "Thorne\\FastOrder\\Tests\\Unit\\FastOrderServiceFullTest::test_transaction_type_validation": 0.025, "Thorne\\FastOrder\\Tests\\Unit\\FastOrderServiceFullTest::test_create_fast_order_data": 0.025, "Thorne\\FastOrder\\Tests\\Unit\\FastOrderServiceFullTest::test_to_array_method": 0.023, "Thorne\\FastOrder\\Tests\\Unit\\FastOrderServiceFullTest::test_keys_method": 0.026, "Thorne\\FastOrder\\Tests\\Unit\\FastOrderServiceFullTest::test_nullable_customer_id": 0.023, "Thorne\\FastOrder\\Tests\\Unit\\FastOrderServiceFullTest::test_handle_transaction_throws_exception_if_customer_not_found_and_no_customer_id": 0.027, "Thorne\\FastOrder\\Tests\\Unit\\FastOrderServiceFullTest::test_handle_transaction_throws_exception_for_missing_required_parameters": 0.023, "Thorne\\FastOrder\\Tests\\Unit\\FastOrderServiceFullTest::test_handle_transaction_throws_exception_for_invalid_hmac_due_to_expired_price_token": 0.026, "Thorne\\FastOrder\\Tests\\Unit\\FastOrderServiceFullTest::test_handle_transaction_throws_exception_for_price_mismatch_in_hmac_validation": 0.023, "Thorne\\FastOrder\\Tests\\Unit\\FastOrderServiceFullTest::test_handle_transaction_throws_exception_for_invalid_signature_in_hmac_validation": 0.031, "Thorne\\FastOrder\\Tests\\Unit\\FastOrderServiceFullTest::test_handle_transaction_throws_exception_for_used_nonce": 0.026, "Thorne\\FastOrder\\Tests\\Unit\\FastOrderServiceFullTest::test_handle_transaction_throws_exception_for_expired_timestamp": 0.024, "Thorne\\FastOrder\\Tests\\Unit\\FastOrderServiceFullTest::test_handle_transaction_throws_exception_for_invalid_transaction_type": 0.026, "Thorne\\FastOrder\\Tests\\Unit\\FastOrderServiceFullTest::test_handle_transaction_throws_exception_when_rate_limit_exceeded": 0.024, "Thorne\\FastOrder\\Tests\\Unit\\FastOrderServiceFullTest::test_handle_transaction_validation_fails_due_to_insufficient_balance": 0.03, "Thorne\\FastOrder\\Tests\\Unit\\FastOrderServiceFullTest::test_handle_transaction_returns_success_for_buy_transaction": 0.024, "Thorne\\FastOrder\\Tests\\Unit\\FastOrderServiceFullTest::test_handle_transaction_returns_success_for_sell_transaction": 0.026, "Thorne\\FastOrder\\Tests\\Unit\\FastOrderServiceFullTest::test_validate_balance_throws_exception_when_api_response_not_successful": 0.022, "Thorne\\FastOrder\\Tests\\Unit\\FastOrderServiceFullTest::test_validate_has_balance_returns_valid_when_sufficient_balance": 0.026, "Thorne\\FastOrder\\Tests\\Unit\\FastOrderServiceFullTest::test_process_transaction_returns_error_when_exception_occurs": 0.026, "Thorne\\FastOrder\\Tests\\Unit\\FastOrderServiceFullTest::test_handle_concurrent_requests_successful_execution": 0.023, "Thorne\\FastOrder\\Tests\\Unit\\FastOrderServiceFullTest::test_validate_price_calls_cache_forget": 0.026, "Thorne\\FastOrder\\Tests\\Unit\\FastOrderServiceFullTest::test_forget_nonce_is_called_after_exception": 0.023, "Thorne\\FastOrder\\Tests\\Unit\\FastOrderServiceFullTest::test_timestamp_diff_accepts_exactly_60_seconds_old": 0.025, "Thorne\\FastOrder\\Tests\\Unit\\FastOrderServiceFullTest::test_timestamp_diff_rejects_61_seconds_old": 0.022, "Thorne\\FastOrder\\Tests\\Unit\\FastOrderServiceFullTest::test_get_priority_boundary_values_lower_limits": 0.025, "Thorne\\FastOrder\\Tests\\Unit\\FastOrderServiceFullTest::test_get_priority_boundary_values_upper_limits": 0.022, "Thorne\\FastOrder\\Tests\\Unit\\FastOrderServiceFullTest::test_validate_hmac_throws_exception_for_malformed_base64_input": 0.026, "Thorne\\FastOrder\\Tests\\Unit\\FastOrderServiceFullTest::test_validate_hmac_throws_exception_for_malformed_json_in_hmac": 0.025, "Thorne\\FastOrder\\Tests\\Unit\\FastOrderServiceFullTest::test_future_timestamp_attack_is_prevented": 0.023, "Thorne\\FastOrder\\Tests\\Unit\\FastOrderServiceFullTest::test_nonce_replay_attack_ttl_happy_path": 0.026, "Thorne\\FastOrder\\Tests\\Unit\\FastOrderServiceFullTest::test_validate_price_correctly_calls_cache_forget": 0.022, "Thorne\\FastOrder\\Tests\\Unit\\FastOrderServiceFullTest::test_process_transaction_dispatches_correct_job_for_buy": 0.027, "Thorne\\FastOrder\\Tests\\Unit\\FastOrderServiceFullTest::test_process_transaction_dispatches_correct_job_for_sell": 0.027, "Thorne\\FastOrder\\Tests\\Unit\\FastOrderServiceFullTest::test_process_transaction_success_for_buy": 0.026, "Thorne\\FastOrder\\Tests\\Unit\\FastOrderServiceFullTest::test_buying_process_job_is_dispatched": 0.032, "Thorne\\FastOrder\\Tests\\Unit\\FastOrderServiceFullTest::test_selling_process_job_is_dispatched": 0.027, "Thorne\\FastOrder\\Tests\\Unit\\FastOrderServiceFullTest::test_rate_limiter_behavior_after_errors": 0.026, "Thorne\\FastOrder\\Tests\\Unit\\FastOrderServiceFullTest::test_semaphore_counters_are_decremented_after_exception": 0.022, "Thorne\\FastOrder\\Tests\\Unit\\FastOrderServiceFullTest::test_buying_process_job_is_dispatched_with_correct_queue": 0.027, "Thorne\\FastOrder\\Tests\\Unit\\FastOrderServiceFullTest::test_rate_limiter_message_format": 0.026, "Thorne\\FastOrder\\Tests\\Unit\\FastOrderServiceFullTest::test_get_safe_message_formats_correctly_for_different_guards": 0.022, "Thorne\\FastOrder\\Tests\\Unit\\FastOrderServiceFullTest::test_config_fallback_behavior": 0.025, "Thorne\\FastOrder\\Tests\\Unit\\FastOrderServiceFullTest::test_process_transaction_fails_when_customer_has_no_default_address": 0.023, "Thorne\\FastOrder\\Tests\\Unit\\FastOrderServiceFullTest::test_validate_hmac_handles_malformed_payload_without_pipe": 0.026, "Thorne\\FastOrder\\Tests\\Unit\\FastOrderServiceFullTest::test_quantity_validation_rejects_zero_or_negative_values": 0.023, "Thorne\\FastOrder\\Tests\\Unit\\FastOrderServiceFullTest::test_db_transaction_rollback_when_exception_occurs": 0.032, "Thorne\\FastOrder\\Tests\\Unit\\FastOrderServiceFullTest::test_handle_concurrent_requests_logs_generic_throwable": 0.025, "Thorne\\FastOrder\\Tests\\Unit\\FastOrderServiceFullTest::test_config_missing_fallback_values": 0.029, "Thorne\\FastOrder\\Tests\\Unit\\FastOrderServiceFullTest::test_concurrent_requests_handling": 0.054, "Thorne\\FastOrder\\Tests\\Unit\\FastOrderServiceFullTest::test_validate_balance_returns_valid_data_when_api_response_successful": 0.023, "Thorne\\FastOrder\\Tests\\Unit\\FastOrderServiceFullTest::test_validate_transaction_returns_true_when_price_and_balance_valid": 0.027, "Thorne\\FastOrder\\Tests\\Unit\\FastOrderServiceFullTest::test_handle_transaction_cleans_cache_on_success": 0.022, "Thorne\\FastOrder\\Tests\\Unit\\FastOrderServiceFullTest::test_rate_limiter_hit_is_called_when_limit_not_exceeded": 0.027, "Thorne\\FastOrder\\Tests\\Unit\\FastOrderServiceFullTest::test_job_dispatch_contains_correct_payload": 0.022, "Thorne\\FastOrder\\Tests\\Unit\\FastOrderServiceFullTest::test_create_price_token_generates_token_with_sell_type": 0.032, "Thorne\\FastOrder\\Tests\\Unit\\FastOrderServiceFullTest::test_create_price_token_generates_token_with_buy_type": 0.044, "Thorne\\FastOrder\\Tests\\Unit\\FastOrderServiceFullTest::test_handle_transaction_throws_exception_for_future_timestamp": 0.022, "Thorne\\Wallet\\Tests\\Unit\\Domain\\ValueObjects\\AccountNumberTest::it_should_generate_valid_account_number_for_supported_currency": 0.002, "Thorne\\Wallet\\Tests\\Unit\\Domain\\ValueObjects\\AccountNumberTest::it_should_throw_exception_when_generating_for_unsupported_currency": 0, "Thorne\\Wallet\\Tests\\Unit\\Domain\\ValueObjects\\AccountNumberTest::it_should_create_account_number_from_valid_string": 0, "Thorne\\Wallet\\Tests\\Unit\\Domain\\ValueObjects\\AccountNumberTest::it_should_create_account_number_from_formatted_string": 0, "Thorne\\Wallet\\Tests\\Unit\\Domain\\ValueObjects\\AccountNumberTest::it_should_throw_exception_for_invalid_length": 0, "Thorne\\Wallet\\Tests\\Unit\\Domain\\ValueObjects\\AccountNumberTest::it_should_throw_exception_for_non_digit_characters": 0, "Thorne\\Wallet\\Tests\\Unit\\Domain\\ValueObjects\\AccountNumberTest::it_should_throw_exception_for_invalid_checksum": 0, "Thorne\\Wallet\\Tests\\Unit\\Domain\\ValueObjects\\AccountNumberTest::it_should_return_correct_currency_iso_code": 0, "Thorne\\Wallet\\Tests\\Unit\\Domain\\ValueObjects\\AccountNumberTest::it_should_return_correct_sequence_number": 0, "Thorne\\Wallet\\Tests\\Unit\\Domain\\ValueObjects\\AccountNumberTest::it_should_return_correct_check_digit": 0, "Thorne\\Wallet\\Tests\\Unit\\Domain\\ValueObjects\\AccountNumberTest::it_should_return_formatted_account_number": 0, "Thorne\\Wallet\\Tests\\Unit\\Domain\\ValueObjects\\AccountNumberTest::it_should_return_masked_account_number": 0, "Thorne\\Wallet\\Tests\\Unit\\Domain\\ValueObjects\\AccountNumberTest::it_should_return_currency_object": 0, "Thorne\\Wallet\\Tests\\Unit\\Domain\\ValueObjects\\AccountNumberTest::it_should_throw_exception_for_unknown_currency_iso_code": 0, "Thorne\\Wallet\\Tests\\Unit\\Domain\\ValueObjects\\AccountNumberTest::it_should_return_correct_account_type": 0, "Thorne\\Wallet\\Tests\\Unit\\Domain\\ValueObjects\\AccountNumberTest::it_should_check_if_belongs_to_currency": 0, "Thorne\\Wallet\\Tests\\Unit\\Domain\\ValueObjects\\AccountNumberTest::it_should_return_correct_region_for_european_currency": 0, "Thorne\\Wallet\\Tests\\Unit\\Domain\\ValueObjects\\AccountNumberTest::it_should_generate_sibling_account_number": 0, "Thorne\\Wallet\\Tests\\Unit\\Domain\\ValueObjects\\AccountNumberTest::it_should_validate_account_number_for_specific_currency": 0, "Thorne\\Wallet\\Tests\\Unit\\Domain\\ValueObjects\\AccountNumberTest::it_should_validate_account_number": 0, "Thorne\\Wallet\\Tests\\Unit\\Domain\\ValueObjects\\AccountNumberTest::it_should_check_equality_between_account_numbers": 0, "Thorne\\Wallet\\Tests\\Unit\\Domain\\ValueObjects\\AccountNumberTest::it_should_convert_to_array": 0, "Thorne\\Wallet\\Tests\\Unit\\Domain\\ValueObjects\\AccountNumberTest::it_should_convert_to_string": 0, "Thorne\\Wallet\\Tests\\Unit\\Domain\\ValueObjects\\AccountNumberTest::it_should_be_json_serializable": 0, "Thorne\\Wallet\\Tests\\Unit\\Domain\\ValueObjects\\AccountNumberTest::it_should_generate_different_account_numbers_on_consecutive_calls": 0, "Thorne\\Wallet\\Tests\\Unit\\Domain\\ValueObjects\\AccountNumberTest::it_should_calculate_correct_luhn_checksum": 0, "Thorne\\Wallet\\Tests\\Unit\\Domain\\ValueObjects\\AccountNumberTest::it_should_handle_edge_case_account_numbers": 0}}