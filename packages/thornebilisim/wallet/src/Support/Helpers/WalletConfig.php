<?php

declare(strict_types=1);

namespace Thorne\Wallet\Support\Helpers;

use Illuminate\Support\Arr;
use InvalidArgumentException;

/**
 * WalletConfig Helper Class.
 *
 * Provides convenient access to all wallet configuration files with type safety,
 * caching, validation, and helper methods for common operations.
 *
 * Configuration files:
 * - audits.php - Audit trail configurations
 * - blockchain.php - Blockchain integration settings
 * - currencies.php - Supported currencies
 * - notifications.php - Notification system settings
 * - payment_methods.php - Payment method configurations
 * - reconciliations.php - Reconciliation process settings
 * - rules.php - Business rules and validation settings
 */
class WalletConfig
{
    /**
     * Configuration cache to avoid repeated file reads.
     */
    private static array $cache = [];

    /**
     * Available configuration files.
     */
    private const CONFIG_FILES = [
        'audits',
        'blockchain',
        'currencies',
        'logging',
        'notifications',
        'payment_methods',
        'reconciliations',
        'rules',
    ];

    /**
     * Get configuration value using dot notation.
     *
     * @param string $key Configuration key (e.g., 'currencies', 'rules.deposit.enabled')
     * @param mixed $default Default value if key not found
     * @return mixed
     */
    public static function get(string $key, mixed $default = null): mixed
    {
        // Handle direct config file access (e.g., 'currencies')
        if (in_array($key, self::CONFIG_FILES)) {
            return self::getConfigFile($key);
        }

        // Handle dot notation (e.g., 'rules.deposit.enabled')
        $parts = explode('.', $key);
        $configFile = array_shift($parts);

        if (! in_array($configFile, self::CONFIG_FILES)) {
            throw new InvalidArgumentException("Invalid configuration file: {$configFile}");
        }

        $config = self::getConfigFile($configFile);

        if (empty($parts)) {
            return $config;
        }

        return Arr::get($config, implode('.', $parts), $default);
    }

    /**
     * Get all currencies configuration.
     *
     * @return array
     */
    public static function currencies(): array
    {
        return self::getConfigFile('currencies');
    }

    /**
     * Get specific currency configuration.
     *
     * @param string $currencyCode Currency code (e.g., 'EUR', 'MLGR')
     * @return array|null
     */
    public static function currency(string $currencyCode): ?array
    {
        $currencies = self::currencies();

        return $currencies[$currencyCode] ?? null;
    }

    /**
     * Check if a currency is enabled.
     *
     * @param string $currencyCode Currency code
     * @return bool
     */
    public static function isCurrencyEnabled(string $currencyCode): bool
    {
        $currency = self::currency($currencyCode);

        return $currency['enabled'] ?? false;
    }

    /**
     * Check if a currency is the default currency.
     *
     * @param string $currencyCode Currency code
     * @return bool
     */
    public static function isCurrencyDefault(string $currencyCode): bool
    {
        $currency = self::currency($currencyCode);

        return $currency['is_default'] ?? false;
    }

    /**
     * Get the default currency.
     *
     * @return array|null
     */
    public static function defaultCurrency(): ?array
    {
        $currencies = self::currencies();

        foreach ($currencies as $code => $currency) {
            if ($currency['is_default'] ?? false) {
                return $currency;
            }
        }

        return null;
    }

    /**
     * Get enabled currencies.
     *
     * @return array
     */
    public static function enabledCurrencies(): array
    {
        $currencies = self::currencies();

        return array_filter($currencies, function ($currency) {
            return $currency['enabled'] ?? false;
        });
    }

    /**
     * Get currency codes.
     *
     * @param bool $enabledOnly Whether to return only enabled currencies
     * @return array
     */
    public static function currencyCodes(bool $enabledOnly = false): array
    {
        $currencies = $enabledOnly ? self::enabledCurrencies() : self::currencies();

        return array_keys($currencies);
    }

    /**
     * Get business rules for a specific operation.
     *
     * @param string $operation Operation type (deposit, withdrawal, transfer, exchange)
     * @return array
     */
    public static function operationRules(string $operation): array
    {
        return self::get("rules.{$operation}", []);
    }

    /**
     * Check if an operation is enabled.
     *
     * @param string $operation Operation type
     * @return bool
     */
    public static function isOperationEnabled(string $operation): bool
    {
        return self::get("rules.{$operation}.enabled", false);
    }

    /**
     * Get payment methods configuration.
     *
     * @return array
     */
    public static function paymentMethods(): array
    {
        return self::get('payment_methods.payment_methods', []);
    }

    /**
     * Get specific payment method configuration.
     *
     * @param string $methodKey Payment method key
     * @return array|null
     */
    public static function paymentMethod(string $methodKey): ?array
    {
        $methods = self::paymentMethods();

        return $methods[$methodKey] ?? null;
    }

    /**
     * Check if a payment method is enabled.
     *
     * @param string $methodKey Payment method key
     * @return bool
     */
    public static function isPaymentMethodEnabled(string $methodKey): bool
    {
        $method = self::paymentMethod($methodKey);

        return $method['enabled'] ?? false;
    }

    /**
     * Get audit configuration.
     *
     * @return array
     */
    public static function audits(): array
    {
        return self::getConfigFile('audits');
    }

    /**
     * Check if audits are enabled.
     *
     * @return bool
     */
    public static function isAuditsEnabled(): bool
    {
        return self::get('audits.enabled', true);
    }

    /**
     * Get blockchain configuration.
     *
     * @return array
     */
    public static function blockchain(): array
    {
        return self::getConfigFile('blockchain');
    }

    /**
     * Get notifications configuration.
     *
     * @return array
     */
    public static function notifications(): array
    {
        return self::getConfigFile('notifications');
    }

    /**
     * Check if notifications are enabled.
     *
     * @return bool
     */
    public static function isNotificationsEnabled(): bool
    {
        return self::get('notifications.enabled', true);
    }

    /**
     * Get reconciliations configuration.
     *
     * @return array
     */
    public static function reconciliations(): array
    {
        return self::getConfigFile('reconciliations');
    }

    /**
     * Check if reconciliations are enabled.
     *
     * @return bool
     */
    public static function isReconciliationsEnabled(): bool
    {
        return self::get('reconciliations.enabled', true);
    }

    /**
     * Validate configuration integrity.
     *
     * @return array Array of validation errors (empty if valid)
     */
    public static function validate(): array
    {
        $errors = [];

        // Validate currencies
        $currencies = self::currencies();
        $defaultCount = 0;

        foreach ($currencies as $code => $currency) {
            if ($currency['is_default'] ?? false) {
                $defaultCount++;
            }

            if (! isset($currency['code']) || $currency['code'] !== $code) {
                $errors[] = "Currency {$code}: code mismatch";
            }
        }

        if ($defaultCount !== 1) {
            $errors[] = "Exactly one currency must be set as default, found: {$defaultCount}";
        }

        // Validate payment methods
        $paymentMethods = self::paymentMethods();
        foreach ($paymentMethods as $key => $method) {
            if (! isset($method['key']) || $method['key'] !== $key) {
                $errors[] = "Payment method {$key}: key mismatch";
            }
        }

        return $errors;
    }

    /**
     * Clear configuration cache.
     *
     * @return void
     */
    public static function clearCache(): void
    {
        self::$cache = [];
    }

    /**
     * Get configuration file contents with caching.
     *
     * @param string $file Configuration file name
     * @return array
     */
    private static function getConfigFile(string $file): array
    {
        if (! isset(self::$cache[$file])) {
            self::$cache[$file] = config("wallet.{$file}", []);
        }

        return self::$cache[$file];
    }

    /**
     * Check if a configuration file exists.
     *
     * @param string $file Configuration file name
     * @return bool
     */
    public static function hasConfigFile(string $file): bool
    {
        return in_array($file, self::CONFIG_FILES);
    }

    /**
     * Get list of available configuration files.
     *
     * @return array
     */
    public static function getConfigFiles(): array
    {
        return self::CONFIG_FILES;
    }

    // ========================================
    // Additional Currency Methods for Value Objects
    // ========================================

    /**
     * Check if a currency is supported (exists in configuration).
     *
     * @param string $currencyCode Currency code
     * @return bool
     */
    public static function isCurrencySupported(string $currencyCode): bool
    {
        return self::currency($currencyCode) !== null;
    }

    /**
     * Get specific currency configuration (alias for currency method).
     *
     * @param string $currencyCode Currency code
     * @return array|null
     */
    public static function getCurrency(string $currencyCode): ?array
    {
        return self::currency($currencyCode);
    }

    /**
     * Get all currencies configuration (alias for currencies method).
     *
     * @return array
     */
    public static function getAllCurrencies(): array
    {
        return self::currencies();
    }

    /**
     * Get the default currency configuration.
     *
     * @return array|null
     */
    public static function getDefaultCurrency(): ?array
    {
        return self::defaultCurrency();
    }

    /**
     * Get array of supported currency codes.
     *
     * @param bool $enabledOnly Whether to return only enabled currencies
     * @return array
     */
    public static function getSupportedCurrencies(bool $enabledOnly = true): array
    {
        return self::currencyCodes($enabledOnly);
    }

    /**
     * Get enabled currencies (alias for enabledCurrencies method).
     *
     * @return array
     */
    public static function getEnabledCurrencies(): array
    {
        return self::enabledCurrencies();
    }

    /**
     * Get custom currencies only.
     *
     * @return array
     */
    public static function getCustomCurrencies(): array
    {
        $currencies = self::currencies();

        return array_filter($currencies, function ($currency) {
            return $currency['is_custom'] ?? false;
        });
    }

    /**
     * Check if a currency is custom.
     *
     * @param string $currencyCode Currency code
     * @return bool
     */
    public static function isCurrencyCustom(string $currencyCode): bool
    {
        $currency = self::currency($currencyCode);

        return $currency['is_custom'] ?? false;
    }

    /**
     * Get currency precision.
     *
     * @param string $currencyCode Currency code
     * @return int
     */
    public static function getCurrencyPrecision(string $currencyCode): int
    {
        $currency = self::currency($currencyCode);

        return $currency['precision'] ?? 2;
    }

    /**
     * Get currency symbol.
     *
     * @param string $currencyCode Currency code
     * @return string
     */
    public static function getCurrencySymbol(string $currencyCode): string
    {
        $currency = self::currency($currencyCode);

        return $currency['symbol'] ?? $currencyCode;
    }
}
