<?php

declare(strict_types=1);

namespace Thorne\Wallet\Infrastructure\Templates;

use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\View;
use Thorne\Wallet\Domain\Exceptions\WalletNotificationException;
use Thorne\Wallet\Domain\Enums\NotificationChannel;
use Thorne\Wallet\Domain\Enums\NotificationType;
use Thorne\Wallet\Support\Facades\WalletLog;

class NotificationTemplateResolver
{
    /**
     * Template directory structure
     */
    private const TEMPLATE_BASE_PATH = 'wallet';
    private const RESOURCE_BASE_PATH = 'notifications';

    /**
     * Resolve template path for given parameters
     *
     * @param NotificationType $type
     * @param NotificationChannel $channel
     * @param string $locale
     * @return string|null
     */
    public function resolve(
        NotificationType $type,
        NotificationChannel $channel,
        string $locale
    ): ?string {
        // Try locale-specific template first
        $template = $this->buildTemplatePath($type, $channel, $locale);
        if ($this->templateExists($template)) {
            return $template;
        }

        // Fall back to default locale
        $defaultLocale = config('app.fallback_locale', 'en');
        if ($locale !== $defaultLocale) {
            $template = $this->buildTemplatePath($type, $channel, $defaultLocale);
            if ($this->templateExists($template)) {
                WalletLog::warning()
                    ->message('Template not found for requested locale, using fallback')
                    ->context([
                        'type' => $type->value,
                        'channel' => $channel->value,
                        'requested_locale' => $locale,
                        'fallback_locale' => $defaultLocale,
                        'template' => $template,
                    ])
                    ->write();
                return $template;
            }
        }

        // Critical: No template found at all
        WalletLog::error()
            ->message('No notification template found')
            ->context([
                'type' => $type->value,
                'channel' => $channel->value,
                'locale' => $locale,
                'fallback_locale' => $defaultLocale,
                'attempted_paths' => [
                    $this->buildTemplatePath($type, $channel, $locale),
                    $this->buildTemplatePath($type, $channel, $defaultLocale),
                ],
            ])
            ->write();

        return null;
    }

    /**
     * Check if template exists
     *
     * @param NotificationType $type
     * @param NotificationChannel $channel
     * @param string $locale
     * @return bool
     */
    public function exists(
        NotificationType $type,
        NotificationChannel $channel,
        string $locale
    ): bool {
        return $this->resolve($type, $channel, $locale) !== null;
    }

    /**
     * Get available locales for a notification type and channel
     *
     * @param NotificationType $type
     * @param NotificationChannel $channel
     * @return array
     */
    public function getAvailableLocales(
        NotificationType $type,
        NotificationChannel $channel
    ): array {
        $locales = [];
        $basePath = $this->getResourcePath($type);

        if (!File::isDirectory($basePath)) {
            return $locales;
        }

        $directories = File::directories($basePath);

        foreach ($directories as $directory) {
            $locale = basename($directory);
            $channelFile = $this->getChannelFileName($channel);
            $templateFile = $directory . '/' . $channelFile;

            if (File::exists($templateFile)) {
                $locales[] = $locale;
            }
        }

        return $locales;
    }

    /**
     * Get all available templates
     *
     * @return array
     */
    public function getAllTemplates(): array
    {
        $templates = [];
        $packagePath = dirname(__DIR__, 2) . '/resources/' . self::RESOURCE_BASE_PATH;

        if (!File::isDirectory($packagePath)) {
            return $templates;
        }

        foreach (NotificationType::cases() as $type) {
            $typePath = $packagePath . '/' . $this->getTypeDirectory($type);

            if (!File::isDirectory($typePath)) {
                continue;
            }

            $localeDirs = File::directories($typePath);

            foreach ($localeDirs as $localeDir) {
                $locale = basename($localeDir);
                $files = File::files($localeDir);

                foreach ($files as $file) {
                    $filename = $file->getFilenameWithoutExtension();
                    $channel = $this->getChannelFromFileName($filename);

                    if ($channel) {
                        $templates[] = [
                            'type' => $type->value,
                            'channel' => $channel->value,
                            'locale' => $locale,
                            'path' => $file->getPathname(),
                        ];
                    }
                }
            }
        }

        return $templates;
    }

    /**
     * Register template namespace with view
     *
     * @return void
     */
    public function registerNamespace(): void
    {
        $packagePath = dirname(__DIR__, 2) . '/resources/' . self::RESOURCE_BASE_PATH;

        View::addNamespace('wallet', $packagePath);
    }

    /**
     * Build template path
     *
     * @param NotificationType $type
     * @param NotificationChannel $channel
     * @param string $locale
     * @return string
     */
    private function buildTemplatePath(
        NotificationType $type,
        NotificationChannel $channel,
        string $locale
    ): string {
        $typeDir = $this->getTypeDirectory($type);
        $channelFile = $this->getChannelFileName($channel);
        
        return self::TEMPLATE_BASE_PATH . "::{$typeDir}.{$locale}.{$channelFile}";
    }

    /**
     * Check if template exists
     *
     * @param string $template
     * @return bool
     */
    private function templateExists(string $template): bool
    {
        return View::exists($template);
    }

    /**
     * Get type directory name
     *
     * @param NotificationType $type
     * @return string
     */
    private function getTypeDirectory(NotificationType $type): string
    {
        return $type->value;
    }

    /**
     * Get channel file name
     *
     * @param NotificationChannel $channel
     * @return string
     */
    private function getChannelFileName(NotificationChannel $channel): string
    {
        return strtolower($channel->value);
    }

    /**
     * Get channel from file name
     *
     * @param string $filename
     * @return NotificationChannel|null
     */
    private function getChannelFromFileName(string $filename): ?NotificationChannel
    {
        foreach (NotificationChannel::cases() as $channel) {
            if ($filename === $channel->value) {
                return $channel;
            }
        }

        return null;
    }

    /**
     * Get resource path for a notification type
     *
     * @param NotificationType $type
     * @return string
     */
    private function getResourcePath(NotificationType $type): string
    {
        $packagePath = dirname(__DIR__, 2) . '/resources/' . self::RESOURCE_BASE_PATH;
        return $packagePath . '/' . $this->getTypeDirectory($type);
    }

}