<?php

namespace Thorne\Wallet\Providers;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\ServiceProvider;
use Thorne\Wallet\Application\Console\Commands\WalletAuditsCleanupCommand;
use Thorne\Wallet\Application\Console\Commands\WalletHealthCheckCommand;
use Thorne\Wallet\Application\Console\Commands\WalletNotificationsCleanupCommand;
use Thorne\Wallet\Application\Console\Commands\WalletNotificationsPublishCommand;
use Thorne\Wallet\Application\Console\Commands\WalletNotificationsRetryCommand;
use Thorne\Wallet\Application\Console\Commands\WalletNotificationsWatchdogCommand;
use Thorne\Wallet\Domain\Services\WalletAuditService;
use Thorne\Wallet\Support\Helpers\WalletConfig;
use Thorne\Wallet\Support\Helpers\WalletDateTime;
use Thorne\Wallet\Support\Helpers\WalletLog;
use Thorne\Wallet\Support\Helpers\WalletMoney;

class WalletServiceProvider extends ServiceProvider
{
    public function register(): void
    {
        $this->registerConfig();
        $this->registerHelpers();
        $this->registerServices();
        $this->registerTemplateNamespace();
    }

    public function boot(): void
    {
        $this->publishConfig();
        $this->publishFactories();
        $this->publishSeeders();
        $this->loadPackageResources();
        $this->loadFactories();
    }

    protected function registerConfig(): void
    {
        $packageRoot = $this->getPackageRoot();

        foreach ($this->getPackageConfigs() as $configKey => $configFile) {
            $configPath = "{$packageRoot}/config/{$configFile}";

            if (file_exists($configPath)) {
                // Special handling for logging channels
                if ($configKey === 'logging.channels') {
                    $this->mergeLoggingChannels($configPath);
                } else {
                    $this->mergeConfigFrom($configPath, $configKey);
                }
            }
        }
    }

    protected function mergeLoggingChannels(string $configPath): void
    {
        $walletLoggingConfig = require $configPath;

        if (isset($walletLoggingConfig['channels']) && is_array($walletLoggingConfig['channels'])) {
            $existingChannels = config('logging.channels', []);
            $mergedChannels = array_merge($existingChannels, $walletLoggingConfig['channels']);

            config(['logging.channels' => $mergedChannels]);
        }
    }

    protected function registerHelpers(): void
    {
        $this->app->singleton(WalletLog::class, function ($app) {
            return new WalletLog;
        });

        $this->app->singleton(WalletConfig::class, function ($app) {
            return new WalletConfig;
        });

        $this->app->singleton(WalletDateTime::class, function ($app) {
            return new WalletDateTime;
        });

        $this->app->singleton(WalletMoney::class, function ($app) {
            return new WalletMoney;
        });
    }

    protected function registerServices(): void
    {
        // Register audit service
        $this->app->singleton(WalletAuditService::class);

        // Register blockchain services
        $this->app->singleton(\Thorne\Wallet\Infrastructure\Http\Clients\BlockchainClient::class);
        $this->app->singleton(\Thorne\Wallet\Application\Services\BlockchainService::class);
        $this->app->singleton(\Thorne\Wallet\Application\HealthCheckers\BlockchainHealthChecker::class);

        // Register notification services
        $this->app->singleton(\Thorne\Wallet\Infrastructure\Templates\NotificationTemplateResolver::class);
        $this->app->singleton(\Thorne\Wallet\Domain\Services\NotificationTemplateService::class);
        $this->app->singleton(\Thorne\Wallet\Application\Services\WalletNotificationService::class);

        // Register facade bindings
        $this->app->bind('wallet.audit', WalletAuditService::class);
        $this->app->bind('wallet.log', WalletLog::class);
    }

    protected function registerRoutes(): void
    {
        $routes = [
            'web' => fn ($path) => $this->loadRoutesFrom($path),
            'api' => fn ($path) => Route::prefix('api')
                ->middleware('api')
                ->group($path),
        ];

        foreach ($routes as $type => $loader) {
            $path = "{$this->getPackageRoot()}/routes/{$type}.php";

            if (! file_exists($path)) {
                continue;
            }

            $loader($path);
        }
    }

    protected function publishConfig(): void
    {
        if (! $this->app->runningInConsole()) {
            return;
        }

        $packageRoot = $this->getPackageRoot();
        $publishableConfigs = [];

        foreach ($this->getPackageConfigs() as $configKey => $configFile) {
            $sourcePath = "{$packageRoot}/config/{$configFile}";
            $targetPath = config_path($configFile);

            if (file_exists($sourcePath)) {
                $publishableConfigs[$sourcePath] = $targetPath;
            }
        }

        $this->publishes($publishableConfigs, 'wallet');
    }

    protected function publishFactories(): void
    {
        if (! $this->app->runningInConsole()) {
            return;
        }

        $packageRoot = $this->getPackageRoot();
        $factoriesPath = "{$packageRoot}/database/factories";

        if (is_dir($factoriesPath)) {
            $this->publishes([
                $factoriesPath => database_path('factories'),
            ], 'wallet-dev');
        }
    }

    protected function publishSeeders(): void
    {
        if (! $this->app->runningInConsole()) {
            return;
        }

        $packageRoot = $this->getPackageRoot();
        $seedersPath = "{$packageRoot}/database/seeders";

        if (is_dir($seedersPath)) {
            $this->publishes([
                $seedersPath => database_path('seeders'),
            ], 'wallet-dev');
        }
    }

    protected function loadPackageResources(): void
    {
        $packageRoot = $this->getPackageRoot();

        // Load migrations
        $migrationsPath = "{$packageRoot}/database/migrations";
        if (is_dir($migrationsPath)) {
            $this->loadMigrationsFrom($migrationsPath);
        }

        // Load route files
        $this->registerRoutes();

        // Register console commands
        $this->registerCommands();

        // Future resource loading will be added here as needed:
        // - Routes (when API endpoints are implemented)
        // - Views (when UI components are added)
        // - Translations (when multi-language support is added)
    }

    protected function registerCommands(): void
    {
        if ($this->app->runningInConsole()) {
            $this->commands([
                WalletHealthCheckCommand::class,

                WalletAuditsCleanupCommand::class,

                WalletNotificationsPublishCommand::class,
                WalletNotificationsRetryCommand::class,
                WalletNotificationsWatchdogCommand::class,
                WalletNotificationsCleanupCommand::class,
            ]);
        }
    }

    protected function getPackageRoot(): string
    {
        return dirname(__DIR__, 2);
    }

    protected function getPackageConfigs(): array
    {
        return [
            'horizon' => 'horizon.php',
            'logging.channels' => 'logging.php',
            'queue' => 'queue.php',
            'event-sourcing' => 'event-sourcing.php',
            'security-layer' => 'security-layer.php',
            'wallet.audits' => 'wallet/audits.php',
            'wallet.blockchain' => 'wallet/blockchain.php',
            'wallet.currencies' => 'wallet/currencies.php',
            'wallet.notifications' => 'wallet/notifications.php',
            'wallet.payment_methods' => 'wallet/payment_methods.php',
            'wallet.reconciliations' => 'wallet/reconciliations.php',
            'wallet.rules' => 'wallet/rules.php',
        ];
    }

    public function provides(): array
    {
        return [
            WalletLog::class,
            WalletConfig::class,
            WalletDateTime::class,
            WalletMoney::class,
            WalletAuditService::class,
        ];
    }

    protected function loadFactories(): void
    {
        // Load factories in all environments, not just during tests
        Factory::guessFactoryNamesUsing(function (string $modelName) {
            // Default Laravel factory namespace
            $defaultNamespace = 'Database\\Factories\\' . class_basename($modelName) . 'Factory';
            if (class_exists($defaultNamespace)) {
                return $defaultNamespace;
            }

            // Wallet package factory namespace
            $walletNamespace = 'Thorne\\Wallet\\Database\\Factories\\' . class_basename($modelName) . 'Factory';
            if (class_exists($walletNamespace)) {
                return $walletNamespace;
            }

            // Fallback to default
            return $defaultNamespace;
        });
    }

    protected function registerTemplateNamespace(): void
    {
        // Register template namespace directly
        $packagePath = $this->getPackageRoot() . '/resources/notifications';
        
        if (is_dir($packagePath)) {
            view()->addNamespace('wallet', $packagePath);
        }
    }
}
