<?php

namespace Thorne\Wallet\Domain\Enums;

enum TransactionDirection: string
{
    case INBOUND = 'INBOUND';
    case OUTBOUND = 'OUTBOUND';

    public static function all(): array
    {
        return array_column(self::cases(), 'value');
    }

    public function getDescription(): string
    {
        return match ($this) {
            self::INBOUND => 'Incoming transaction (credit)',
            self::OUTBOUND => 'Outgoing transaction (debit)',
        };
    }

    public function isInbound(): bool
    {
        return $this === self::INBOUND;
    }

    public function isOutbound(): bool
    {
        return $this === self::OUTBOUND;
    }

    public function getOpposite(): self
    {
        return match ($this) {
            self::INBOUND => self::OUTBOUND,
            self::OUTBOUND => self::INBOUND,
        };
    }

    public function getBalanceModifier(): int
    {
        return match ($this) {
            self::INBOUND => 1,
            self::OUTBOUND => -1,
        };
    }

    public function getAccountingType(): string
    {
        return match ($this) {
            self::INBOUND => 'credit',
            self::OUTBOUND => 'debit',
        };
    }
}