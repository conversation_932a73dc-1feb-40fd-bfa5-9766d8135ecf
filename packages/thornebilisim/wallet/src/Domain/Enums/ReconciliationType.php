<?php

namespace Thorne\Wallet\Domain\Enums;

enum ReconciliationType: string
{
    case PERIODIC = 'PERIODIC';
    case POST_TRANSACTION = 'POST_TRANSACTION';
    case MANUAL = 'MANUAL';

    public static function all(): array
    {
        return array_column(self::cases(), 'value');
    }

    public function getDescription(): string
    {
        return match ($this) {
            self::PERIODIC => 'Scheduled periodic reconciliation',
            self::POST_TRANSACTION => 'Reconciliation triggered after transaction',
            self::MANUAL => 'Manual reconciliation initiated by user',
        };
    }

    public function isAutomated(): bool
    {
        return $this === self::PERIODIC || $this === self::POST_TRANSACTION;
    }

    public function isManual(): bool
    {
        return $this === self::MANUAL;
    }

    public function isTriggered(): bool
    {
        return $this === self::POST_TRANSACTION;
    }

    public function isScheduled(): bool
    {
        return $this === self::PERIODIC;
    }

    public static function automatedTypes(): array
    {
        return [
            self::PERIODIC,
            self::POST_TRANSACTION,
        ];
    }
}