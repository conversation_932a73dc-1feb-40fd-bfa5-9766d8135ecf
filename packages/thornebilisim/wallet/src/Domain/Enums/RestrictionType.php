<?php

namespace Thorne\Wallet\Domain\Enums;

enum RestrictionType: string
{
    case ALL = 'ALL';
    case DEPOSIT = 'DEPOSIT';
    case WITHDRAWAL = 'WITHDRAWAL';
    case TRANSFER = 'TRANSFER';

    public static function all(): array
    {
        return array_column(self::cases(), 'value');
    }

    public function getDescription(): string
    {
        return match ($this) {
            self::ALL => 'All operations are restricted',
            self::DEPOSIT => 'Deposit operations are restricted',
            self::WITHDRAWAL => 'Withdrawal operations are restricted',
            self::TRANSFER => 'Transfer operations are restricted',
        };
    }

    public function isAllOperations(): bool
    {
        return $this === self::ALL;
    }

    public function restrictsOperation(string $operation): bool
    {
        return $this === self::ALL || $this->value === strtoupper($operation);
    }

    public static function transactionTypes(): array
    {
        return [
            self::DEPOSIT,
            self::WITHDRAWAL,
            self::TRANSFER,
        ];
    }
}