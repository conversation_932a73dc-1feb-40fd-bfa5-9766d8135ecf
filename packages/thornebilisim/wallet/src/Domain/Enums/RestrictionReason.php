<?php

namespace Thorne\Wallet\Domain\Enums;

enum RestrictionReason: string
{
    case OPERATION_BLOCKED = 'OPERATION_BLOCKED';
    case TEMPORARY_SUSPENDED = 'TEMPORARY_SUSPENDED';
    case PERMANENT_SUSPENDED = 'PERMANENT_SUSPENDED';

    public static function all(): array
    {
        return array_column(self::cases(), 'value');
    }

    public function getDescription(): string
    {
        return match ($this) {
            self::OPERATION_BLOCKED => 'Operation is blocked due to security reasons',
            self::TEMPORARY_SUSPENDED => 'Account is temporarily suspended',
            self::PERMANENT_SUSPENDED => 'Account is permanently suspended',
        };
    }

    public function isTemporary(): bool
    {
        return $this === self::TEMPORARY_SUSPENDED;
    }

    public function isPermanent(): bool
    {
        return $this === self::PERMANENT_SUSPENDED;
    }

    public function isOperationBlocked(): bool
    {
        return $this === self::OPERATION_BLOCKED;
    }

    public function getSeverityLevel(): int
    {
        return match ($this) {
            self::OPERATION_BLOCKED => 1,
            self::TEMPORARY_SUSPENDED => 2,
            self::PERMANENT_SUSPENDED => 3,
        };
    }
}