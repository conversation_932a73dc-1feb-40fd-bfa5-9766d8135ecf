<?php

namespace Thorne\Wallet\Domain\Enums;

enum DiscrepancyDirection: string
{
    case INBOUND = 'INBOUND';
    case OUTBOUND = 'OUTBOUND';

    public static function all(): array
    {
        return array_column(self::cases(), 'value');
    }

    public function getDescription(): string
    {
        return match ($this) {
            self::INBOUND => 'Discrepancy in incoming funds or transactions',
            self::OUTBOUND => 'Discrepancy in outgoing funds or transactions',
        };
    }

    public function isInbound(): bool
    {
        return $this === self::INBOUND;
    }

    public function isOutbound(): bool
    {
        return $this === self::OUTBOUND;
    }

    public function getOpposite(): self
    {
        return match ($this) {
            self::INBOUND => self::OUTBOUND,
            self::OUTBOUND => self::INBOUND,
        };
    }

    public function getImpactOnBalance(): string
    {
        return match ($this) {
            self::INBOUND => 'positive',
            self::OUTBOUND => 'negative',
        };
    }
}