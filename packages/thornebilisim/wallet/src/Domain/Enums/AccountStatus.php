<?php

namespace Thorne\Wallet\Domain\Enums;

enum AccountStatus: string
{
    case ACTIVE = 'ACTIVE';
    case SUSPENDED = 'SUSPENDED';
    case CLOSED = 'CLOSED';

    public static function all(): array
    {
        return array_column(self::cases(), 'value');
    }

    public function getDescription(): string
    {
        return match ($this) {
            self::ACTIVE => 'Account is active and operational',
            self::SUSPENDED => 'Account is temporarily suspended',
            self::CLOSED => 'Account is permanently closed',
        };
    }

    public function isOperational(): bool
    {
        return $this === self::ACTIVE;
    }

    public function canPerformTransactions(): bool
    {
        return $this === self::ACTIVE;
    }
}