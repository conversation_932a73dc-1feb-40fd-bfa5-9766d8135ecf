<?php

namespace Thorne\Wallet\Domain\Enums;

enum ReconciliationStatus: string
{
    case PENDING = 'PENDING';
    case CHECKING = 'CHECKING';
    case APPROVED = 'APPROVED';
    case REJECTED = 'REJECTED';
    case FAILED = 'FAILED';

    public static function all(): array
    {
        return array_column(self::cases(), 'value');
    }

    public function getDescription(): string
    {
        return match ($this) {
            self::PENDING => 'Reconciliation is waiting to be processed',
            self::CHECKING => 'Reconciliation is currently being checked',
            self::APPROVED => 'Reconciliation has been approved',
            self::REJECTED => 'Reconciliation has been rejected',
            self::FAILED => 'Reconciliation failed due to technical issues',
        };
    }

    public function isCompleted(): bool
    {
        return in_array($this, [self::APPROVED, self::REJECTED, self::FAILED]);
    }

    public function isInProgress(): bool
    {
        return in_array($this, [self::PENDING, self::CHECKING]);
    }

    public function isSuccessful(): bool
    {
        return $this === self::APPROVED;
    }

    public function isUnsuccessful(): bool
    {
        return $this === self::REJECTED || $this === self::FAILED;
    }

    public static function finalStatuses(): array
    {
        return [
            self::APPROVED,
            self::REJECTED,
            self::FAILED,
        ];
    }

    public static function processingStatuses(): array
    {
        return [
            self::PENDING,
            self::CHECKING,
        ];
    }
}