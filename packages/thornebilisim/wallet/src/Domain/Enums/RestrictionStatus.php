<?php

namespace Thorne\Wallet\Domain\Enums;

enum RestrictionStatus: string
{
    case APPLIED = 'APPLIED';
    case LIFTED = 'LIFTED';
    case EXPIRED = 'EXPIRED';

    public static function all(): array
    {
        return array_column(self::cases(), 'value');
    }

    public function getDescription(): string
    {
        return match ($this) {
            self::APPLIED => 'Restriction is currently active',
            self::LIFTED => 'Restriction has been manually lifted',
            self::EXPIRED => 'Restriction has expired automatically',
        };
    }

    public function isActive(): bool
    {
        return $this === self::APPLIED;
    }

    public function isInactive(): bool
    {
        return $this === self::LIFTED || $this === self::EXPIRED;
    }

    public function wasManuallyRemoved(): bool
    {
        return $this === self::LIFTED;
    }

    public function wasAutomaticallyRemoved(): bool
    {
        return $this === self::EXPIRED;
    }

    public static function inactiveStatuses(): array
    {
        return [
            self::LIFTED,
            self::EXPIRED,
        ];
    }
}