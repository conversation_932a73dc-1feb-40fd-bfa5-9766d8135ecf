<?php

namespace Thorne\Wallet\Domain\Enums;

enum ArchiveStatus: string
{
    case ACTIVE = 'ACTIVE';
    case ARCHIVED = 'ARCHIVED';

    public static function all(): array
    {
        return array_column(self::cases(), 'value');
    }

    public function getDescription(): string
    {
        return match ($this) {
            self::ACTIVE => 'Record is active and accessible',
            self::ARCHIVED => 'Record is archived for historical purposes',
        };
    }

    public function isAccessible(): bool
    {
        return $this === self::ACTIVE;
    }

    public function isArchived(): bool
    {
        return $this === self::ARCHIVED;
    }
}