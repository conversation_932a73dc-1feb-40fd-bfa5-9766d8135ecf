<?php

namespace Thorne\Wallet\Domain\Enums;

enum TransactionType: string
{
    case DEPOSIT = 'DEPOSIT';
    case WITHDRAWAL = 'WITHDRAWAL';
    case TRANSFER = 'TRANSFER';
    case EXCHANGE = 'EXCHANGE';
    case FEE = 'FEE';
    case ADJUSTMENT = 'ADJUSTMENT';
    case REVERSAL = 'REVERSAL';
    case REFUND = 'REFUND';

    public static function all(): array
    {
        return array_column(self::cases(), 'value');
    }

    public function getDescription(): string
    {
        return match ($this) {
            self::DEPOSIT => 'Deposit transaction',
            self::WITHDRAWAL => 'Withdrawal transaction',
            self::TRANSFER => 'Transfer transaction between accounts',
            self::EXCHANGE => 'Currency exchange transaction',
            self::FEE => 'Fee transaction',
            self::ADJUSTMENT => 'Balance adjustment transaction',
            self::REVERSAL => 'Transaction reversal',
            self::REFUND => 'Refund transaction',
        };
    }

    public function requiresApproval(): bool
    {
        return in_array($this, [
            self::WITHDRAWAL,
            self::ADJUSTMENT,
            self::REVERSAL,
        ]);
    }

    public function isSystemGenerated(): bool
    {
        return in_array($this, [
            self::FEE,
            self::ADJUSTMENT,
            self::REVERSAL,
            self::REFUND,
        ]);
    }

    public function isUserInitiated(): bool
    {
        return in_array($this, [
            self::DEPOSIT,
            self::WITHDRAWAL,
            self::TRANSFER,
            self::EXCHANGE,
        ]);
    }

    public function affectsBalance(): bool
    {
        return true;
    }

    public function getDefaultDirection(): TransactionDirection
    {
        return match ($this) {
            self::DEPOSIT, self::REFUND => TransactionDirection::INBOUND,
            self::WITHDRAWAL, self::FEE => TransactionDirection::OUTBOUND,
            default => TransactionDirection::OUTBOUND,
        };
    }

    public static function userInitiatedTypes(): array
    {
        return [
            self::DEPOSIT,
            self::WITHDRAWAL,
            self::TRANSFER,
            self::EXCHANGE,
        ];
    }

    public static function systemGeneratedTypes(): array
    {
        return [
            self::FEE,
            self::ADJUSTMENT,
            self::REVERSAL,
            self::REFUND,
        ];
    }
}