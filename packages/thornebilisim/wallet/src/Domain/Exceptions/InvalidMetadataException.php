<?php

declare(strict_types=1);

namespace Thorne\Wallet\Domain\Exceptions;

/**
 * InvalidMetadataException.
 *
 * Exception thrown when Metadata value object validation fails due to invalid
 * data, malformed URLs, IP addresses, or context information.
 */
class InvalidMetadataException extends WalletDomainException
{
    // Error codes
    public const INVALID_IP_ADDRESS = 5001;
    public const INVALID_URL = 5002;
    public const INVALID_PERFORMED_BY = 5003;
    public const INVALID_CONTEXT = 5004;
    public const METADATA_TOO_LARGE = 5005;

    /**
     * Create exception for invalid IP address.
     */
    public static function invalidIpAddress(string $ipAddress, array $context = []): self
    {
        return new self(
            "Invalid IP address: {$ipAddress}",
            self::INVALID_IP_ADDRESS,
            null,
            array_merge(['ip_address' => $ipAddress], $context)
        );
    }

    /**
     * Create exception for invalid URL.
     */
    public static function invalidUrl(string $url, array $context = []): self
    {
        return new self(
            "Invalid URL: {$url}",
            self::INVALID_URL,
            null,
            array_merge(['url' => $url], $context)
        );
    }

    /**
     * Create exception for invalid performedBy structure.
     */
    public static function invalidPerformedBy(string $reason, array $performedBy = [], array $context = []): self
    {
        return new self(
            "Invalid performedBy structure: {$reason}",
            self::INVALID_PERFORMED_BY,
            null,
            array_merge(['performed_by' => $performedBy, 'reason' => $reason], $context)
        );
    }

    /**
     * Create exception for invalid context data.
     */
    public static function invalidContext(string $reason, array $context = []): self
    {
        return new self(
            "Invalid metadata context: {$reason}",
            self::INVALID_CONTEXT,
            null,
            array_merge(['reason' => $reason], $context)
        );
    }

    /**
     * Create exception for metadata that is too large.
     */
    public static function metadataTooLarge(int $size, int $maxSize, array $context = []): self
    {
        return new self(
            "Metadata too large: {$size} bytes (max: {$maxSize} bytes)",
            self::METADATA_TOO_LARGE,
            null,
            array_merge(['size' => $size, 'max_size' => $maxSize], $context)
        );
    }

    /**
     * Create exception for general validation failures.
     */
    public static function validationFailed(string $field, string $reason = '', array $context = []): self
    {
        $message = "Metadata validation failed for field '{$field}'";
        if ($reason) {
            $message .= ": {$reason}";
        }

        return new self(
            $message,
            self::INVALID_CONTEXT,
            null,
            array_merge(['field' => $field, 'reason' => $reason], $context)
        );
    }
}
