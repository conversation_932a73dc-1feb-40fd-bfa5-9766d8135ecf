<?php

declare(strict_types=1);

namespace Thorne\Wallet\Domain\Exceptions;

class WalletNotificationException extends WalletDomainException
{
    public static function disabled(): self
    {
        return new self('Wallet notifications are disabled');
    }

    public static function channelNotConfigured(string $channel): self
    {
        return new self("Channel {$channel} not configured");
    }

    public static function channelDisabled(string $channel): self
    {
        return new self("Notification channel {$channel} is disabled");
    }

    public static function recipientNotFound(int $recipientId, string $type): self
    {
        return new self("Recipient {$type}#{$recipientId} not found");
    }

    public static function recipientClassNotConfigured(string $type): self
    {
        return new self("Recipient class not configured for type: {$type}");
    }

    public static function templateNotFound(string $type, string $channel, string $language): self
    {
        return new self("Template not found: {$type}/{$channel}/{$language}");
    }

    public static function templateRenderFailed(string $type, string $channel, string $language, string $error): self
    {
        return new self("Template render failed for {$type}/{$channel}/{$language}: {$error}");
    }

    public static function channelSendFailed(string $channel, string $error): self
    {
        return new self("Failed to send via {$channel}: {$error}");
    }

    public static function notificationNotScheduled(int $notificationId): self
    {
        return new self("Notification {$notificationId} is not scheduled");
    }

    public static function maxRetryAttemptsReached(int $notificationId, int $maxAttempts): self
    {
        return new self("Notification {$notificationId} reached max retry attempts: {$maxAttempts}");
    }

    public static function invalidNotificationStatus(int $notificationId, string $currentStatus, string $expectedStatus): self
    {
        return new self("Notification {$notificationId} has invalid status {$currentStatus}, expected {$expectedStatus}");
    }
}