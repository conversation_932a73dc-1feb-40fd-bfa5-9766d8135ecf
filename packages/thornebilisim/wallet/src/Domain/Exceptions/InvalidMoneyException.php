<?php

declare(strict_types=1);

namespace Thorne\Wallet\Domain\Exceptions;

/**
 * InvalidMoneyException.
 *
 * Exception thrown when Money value object operations fail due to invalid data,
 * unsupported currencies, or arithmetic errors.
 */
class InvalidMoneyException extends WalletDomainException
{
    // Error codes
    public const UNSUPPORTED_CURRENCY = 2001;
    public const INVALID_AMOUNT = 2002;
    public const CURRENCY_MISMATCH = 2003;
    public const ARITHMETIC_ERROR = 2004;
    public const PRECISION_ERROR = 2005;

    /**
     * Create exception for unsupported currency.
     */
    public static function unsupportedCurrency(string $currency, array $context = []): self
    {
        return new self(
            "Unsupported currency: {$currency}",
            self::UNSUPPORTED_CURRENCY,
            null,
            array_merge(['currency' => $currency], $context)
        );
    }

    /**
     * Create exception for invalid amount.
     */
    public static function invalidAmount(string $amount, string $reason = '', array $context = []): self
    {
        $message = "Invalid amount: {$amount}";
        if ($reason) {
            $message .= " ({$reason})";
        }

        return new self(
            $message,
            self::INVALID_AMOUNT,
            null,
            array_merge(['amount' => $amount, 'reason' => $reason], $context)
        );
    }

    /**
     * Create exception for currency mismatch.
     */
    public static function currencyMismatch(string $currency1, string $currency2, array $context = []): self
    {
        return new self(
            "Currency mismatch: {$currency1} vs {$currency2}",
            self::CURRENCY_MISMATCH,
            null,
            array_merge(['currency1' => $currency1, 'currency2' => $currency2], $context)
        );
    }

    /**
     * Create exception for arithmetic errors.
     */
    public static function arithmeticError(string $operation, string $reason = '', array $context = []): self
    {
        $message = "Arithmetic error in {$operation}";
        if ($reason) {
            $message .= ": {$reason}";
        }

        return new self(
            $message,
            self::ARITHMETIC_ERROR,
            null,
            array_merge(['operation' => $operation, 'reason' => $reason], $context)
        );
    }

    /**
     * Create exception for precision errors.
     */
    public static function precisionError(string $message, array $context = []): self
    {
        return new self(
            "Precision error: {$message}",
            self::PRECISION_ERROR,
            null,
            $context
        );
    }
}
