<?php

declare(strict_types=1);

namespace Thorne\Wallet\Domain\Exceptions;

/**
 * UnsupportedCurrencyException.
 *
 * Exception thrown when attempting to use a currency that is not supported
 * by the wallet system configuration.
 */
class UnsupportedCurrencyException extends WalletDomainException
{
    // Error codes
    public const CURRENCY_NOT_FOUND = 3001;
    public const CURRENCY_DISABLED = 3002;
    public const CURRENCY_NOT_CONFIGURED = 3003;

    /**
     * Create exception for currency not found in configuration.
     */
    public static function notFound(string $currency, array $context = []): self
    {
        return new self(
            "Currency not found: {$currency}",
            self::CURRENCY_NOT_FOUND,
            null,
            array_merge(['currency' => $currency], $context)
        );
    }

    /**
     * Create exception for disabled currency.
     */
    public static function disabled(string $currency, array $context = []): self
    {
        return new self(
            "Currency is disabled: {$currency}",
            self::CURRENCY_DISABLED,
            null,
            array_merge(['currency' => $currency], $context)
        );
    }

    /**
     * Create exception for currency not properly configured.
     */
    public static function notConfigured(string $currency, string $reason = '', array $context = []): self
    {
        $message = "Currency not properly configured: {$currency}";
        if ($reason) {
            $message .= " ({$reason})";
        }

        return new self(
            $message,
            self::CURRENCY_NOT_CONFIGURED,
            null,
            array_merge(['currency' => $currency, 'reason' => $reason], $context)
        );
    }

    /**
     * Create exception for general currency support issues.
     */
    public static function notSupported(string $currency, array $context = []): self
    {
        return new self(
            "Currency not supported: {$currency}",
            self::CURRENCY_NOT_FOUND,
            null,
            array_merge(['currency' => $currency], $context)
        );
    }
}
