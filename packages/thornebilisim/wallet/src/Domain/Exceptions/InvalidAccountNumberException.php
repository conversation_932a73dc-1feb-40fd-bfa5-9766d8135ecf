<?php

declare(strict_types=1);

namespace Thorne\Wallet\Domain\Exceptions;

/**
 * InvalidAccountNumberException.
 *
 * Exception thrown when account number validation fails due to invalid format,
 * checksum errors, or generation issues.
 */
class InvalidAccountNumberException extends WalletDomainException
{
    // Error codes
    public const INVALID_FORMAT = 4001;
    public const INVALID_CHECKSUM = 4002;
    public const INVALID_LENGTH = 4003;
    public const INVALID_CURRENCY_CODE = 4004;
    public const GENERATION_FAILED = 4005;

    /**
     * Create exception for invalid format.
     */
    public static function invalidFormat(string $accountNumber, string $reason = '', array $context = []): self
    {
        $message = "Invalid account number format: {$accountNumber}";
        if ($reason) {
            $message .= " ({$reason})";
        }

        return new self(
            $message,
            self::INVALID_FORMAT,
            null,
            array_merge(['account_number' => $accountNumber, 'reason' => $reason], $context)
        );
    }

    /**
     * Create exception for invalid checksum.
     */
    public static function invalidChecksum(string $accountNumber, array $context = []): self
    {
        return new self(
            "Invalid account number checksum: {$accountNumber}",
            self::INVALID_CHECKSUM,
            null,
            array_merge(['account_number' => $accountNumber], $context)
        );
    }

    /**
     * Create exception for invalid length.
     */
    public static function invalidLength(string $accountNumber, int $expectedLength, array $context = []): self
    {
        $actualLength = strlen($accountNumber);
        
        return new self(
            "Invalid account number length: expected {$expectedLength} digits, got {$actualLength}",
            self::INVALID_LENGTH,
            null,
            array_merge([
                'account_number' => $accountNumber,
                'expected_length' => $expectedLength,
                'actual_length' => $actualLength
            ], $context)
        );
    }

    /**
     * Create exception for invalid currency code.
     */
    public static function invalidCurrencyCode(string $currencyCode, array $context = []): self
    {
        return new self(
            "Invalid currency ISO code: {$currencyCode}",
            self::INVALID_CURRENCY_CODE,
            null,
            array_merge(['currency_code' => $currencyCode], $context)
        );
    }

    /**
     * Create exception for generation failures.
     */
    public static function generationFailed(string $reason = '', array $context = []): self
    {
        $message = "Account number generation failed";
        if ($reason) {
            $message .= ": {$reason}";
        }

        return new self(
            $message,
            self::GENERATION_FAILED,
            null,
            array_merge(['reason' => $reason], $context)
        );
    }

    /**
     * Create exception for general validation failures.
     */
    public static function validationFailed(string $accountNumber, string $reason = '', array $context = []): self
    {
        $message = "Account number validation failed: {$accountNumber}";
        if ($reason) {
            $message .= " ({$reason})";
        }

        return new self(
            $message,
            self::INVALID_FORMAT,
            null,
            array_merge(['account_number' => $accountNumber, 'reason' => $reason], $context)
        );
    }
}
