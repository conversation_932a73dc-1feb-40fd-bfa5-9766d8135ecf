<?php

declare(strict_types=1);

namespace Thorne\Wallet\Domain\Services;

use Illuminate\Support\Facades\View;
use Illuminate\Support\Str;
use Thorne\Wallet\Domain\Exceptions\WalletNotificationException;
use Thorne\Wallet\Domain\Enums\NotificationChannel;
use Thorne\Wallet\Domain\Enums\NotificationType;
use Thorne\Wallet\Infrastructure\Templates\NotificationTemplateResolver;
use Thorne\Wallet\Support\Facades\WalletLog;

class NotificationTemplateService
{
    public function __construct(
        private readonly NotificationTemplateResolver $templateResolver
    ) {
    }

    /**
     * Render notification content using appropriate template
     *
     * @param NotificationType $type
     * @param NotificationChannel $channel
     * @param string $locale
     * @param array $data
     * @return array
     */
    public function render(
        NotificationType $type,
        NotificationChannel $channel,
        string $locale,
        array $data = []
    ): array {
        $template = $this->templateResolver->resolve($type, $channel, $locale);
        
        if (!$template) {
            throw WalletNotificationException::templateNotFound(
                $type->value,
                $channel->value,
                $locale
            );
        }

        return $this->renderTemplate($template, $data, $channel);
    }

    /**
     * Check if template exists for given parameters
     *
     * @param NotificationType $type
     * @param NotificationChannel $channel
     * @param string $locale
     * @return bool
     */
    public function hasTemplate(
        NotificationType $type,
        NotificationChannel $channel,
        string $locale
    ): bool {
        return $this->templateResolver->exists($type, $channel, $locale);
    }

    /**
     * Get available locales for a notification type and channel
     *
     * @param NotificationType $type
     * @param NotificationChannel $channel
     * @return array
     */
    public function getAvailableLocales(
        NotificationType $type,
        NotificationChannel $channel
    ): array {
        return $this->templateResolver->getAvailableLocales($type, $channel);
    }

    /**
     * Render template with data
     *
     * @param string $template
     * @param array $data
     * @param NotificationChannel $channel
     * @return array
     */
    private function renderTemplate(string $template, array $data, NotificationChannel $channel): array
    {
        $content = [];

        try {
            // Email channel has subject and body
            if ($channel === NotificationChannel::EMAIL) {
                if (View::exists($template . '.subject')) {
                    $content['subject'] = View::make($template . '.subject', $data)->render();
                } else {
                    // Extract subject from template if defined
                    $fullContent = View::make($template, $data)->render();
                    if (preg_match('/@subject\([\'"]([^\'"]*)[\'"]?\)/', $fullContent, $matches)) {
                        $content['subject'] = $matches[1];
                        $content['body'] = preg_replace('/@subject\([^\)]*\)\s*/', '', $fullContent);
                    } else {
                        WalletLog::warning()
                            ->message('Email template has no subject directive, using default')
                            ->context([
                                'template' => $template,
                                'channel' => $channel->value,
                            ])
                            ->write();
                        $content['subject'] = $this->generateDefaultSubject($data);
                        $content['body'] = $fullContent;
                    }
                }
                
                // Clean up body
                $content['body'] = trim($content['body']);
            } 
        // SMS and PUSH have message
        elseif (in_array($channel, [NotificationChannel::SMS, NotificationChannel::PUSH])) {
            $content['message'] = View::make($template, $data)->render();
            $content['message'] = $this->cleanMessage($content['message']);
            
            // For push notifications, try to extract title
            if ($channel === NotificationChannel::PUSH) {
                if (View::exists($template . '.title')) {
                    $content['title'] = View::make($template . '.title', $data)->render();
                } elseif (preg_match('/@title\((.*?)\)/', $content['message'], $matches)) {
                    $content['title'] = trim($matches[1], '"\'');
                    $content['message'] = preg_replace('/@title\(.*?\)/', '', $content['message']);
                } else {
                    $content['title'] = config('app.name', 'Wallet');
                }
                
                $content['title'] = $this->cleanMessage($content['title']);
            }
        }
        // ALERT channel has level and message
        elseif ($channel === NotificationChannel::ALERT) {
            $fullContent = View::make($template, $data)->render();
            
            // Extract alert level if defined
            if (preg_match('/@level\((.*?)\)/', $fullContent, $matches)) {
                $content['level'] = trim($matches[1], '"\'');
                $content['message'] = preg_replace('/@level\(.*?\)/', '', $fullContent);
            } else {
                $content['level'] = 'info';
                $content['message'] = $fullContent;
            }
            
            $content['message'] = $this->cleanMessage($content['message']);
        }

            // Add metadata
            $content['rendered_at'] = now()->toIso8601String();
            $content['template'] = $template;
            $content['locale'] = $data['locale'] ?? app()->getLocale();

            return $content;
        } catch (\Exception $e) {
            WalletLog::error()
                ->message('Failed to render notification template')
                ->context([
                    'template' => $template,
                    'channel' => $channel->value,
                    'error' => $e->getMessage(),
                    'data_keys' => array_keys($data),
                ])
                ->write();
            
            throw WalletNotificationException::templateRenderFailed(
                basename($template),
                $channel->value,
                $data['locale'] ?? app()->getLocale(),
                $e->getMessage()
            );
        }
    }

    /**
     * Clean message by removing extra whitespace and newlines
     *
     * @param string $message
     * @return string
     */
    private function cleanMessage(string $message): string
    {
        // Remove Blade comments
        $message = preg_replace('/{{--.*?--}}/s', '', $message);
        
        // Remove extra whitespace
        $message = preg_replace('/\s+/', ' ', $message);
        
        // Trim
        return trim($message);
    }

    /**
     * Generate default subject for email notifications
     *
     * @param array $data
     * @return string
     */
    private function generateDefaultSubject(array $data): string
    {
        $appName = config('app.name', 'Wallet');
        
        if (isset($data['notification']) && isset($data['notification']->type)) {
            $type = $data['notification']->type;
            return sprintf('[%s] %s', $appName, Str::headline($type));
        }
        
        return sprintf('[%s] Notification', $appName);
    }

    /**
     * Validate template data
     *
     * @param array $data
     * @param array $required
     * @return bool
     * @throws \InvalidArgumentException
     */
    public function validateTemplateData(array $data, array $required): bool
    {
        $missing = array_diff($required, array_keys($data));
        
        if (!empty($missing)) {
            throw new \InvalidArgumentException(
                'Missing required template data: ' . implode(', ', $missing)
            );
        }
        
        return true;
    }

    /**
     * Preview template without sending
     *
     * @param NotificationType $type
     * @param NotificationChannel $channel
     * @param string $locale
     * @param array $data
     * @return array
     */
    public function preview(
        NotificationType $type,
        NotificationChannel $channel,
        string $locale,
        array $data = []
    ): array {
        // Add sample data for preview
        $previewData = array_merge([
            'preview_mode' => true,
            'recipient' => (object) [
                'name' => 'John Doe',
                'email' => '<EMAIL>',
                'phone' => '+1234567890',
            ],
            'notification' => (object) [
                'type' => $type,
                'channel' => $channel,
                'created_at' => now(),
            ],
        ], $data);
        
        return $this->render($type, $channel, $locale, $previewData);
    }
}