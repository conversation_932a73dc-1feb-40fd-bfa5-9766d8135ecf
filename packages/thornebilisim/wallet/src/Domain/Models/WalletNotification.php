<?php

namespace Thorne\Wallet\Domain\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Thorne\Wallet\Database\Factories\WalletNotificationFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Support\Carbon;
use Thorne\Wallet\Domain\Enums\NotificationChannel;
use Thorne\Wallet\Domain\Enums\NotificationStatus;
use Thorne\Wallet\Domain\Enums\NotificationType;
use Thorne\Wallet\Domain\Enums\RecipientType;
use Thorne\Wallet\Support\Helpers\WalletConfig;
use Thorne\Wallet\Support\Traits\MakesUuid;
use Thorne\Wallet\Domain\Exceptions\WalletNotificationException;

class WalletNotification extends Model
{
    use HasFactory, MakesUuid;

    protected $table = 'wallet_notifications';

    protected $fillable = [
        'uuid',
        'correlation_id',
        'recipient_id',
        'recipient_type',
        'notification_type',
        'notification_status',
        'notification_channel',
        'language',
        'notification_data',
        'notifiable_type',
        'notifiable_id',
        'scheduled_at',
        'sent_at',
        'failed_at',
        'failed_reason',
        'retry_count',
        'read_at',
    ];

    protected $casts = [
        'notification_type' => NotificationType::class,
        'notification_status' => NotificationStatus::class,
        'notification_channel' => NotificationChannel::class,
        'recipient_type' => RecipientType::class,
        'notification_data' => 'json',
        'scheduled_at' => 'datetime',
        'sent_at' => 'datetime',
        'failed_at' => 'datetime',
        'read_at' => 'datetime',
        'retry_count' => 'integer',
    ];

    /**
     * Get the recipient of this notification.
     */
    public function recipient(): MorphTo
    {
        return $this->morphTo('recipient', 'recipient_type', 'recipient_id');
    }

    /**
     * Get the notifiable entity that triggered this notification.
     */
    public function notifiable(): MorphTo
    {
        return $this->morphTo('notifiable', 'notifiable_type', 'notifiable_id');
    }

    /**
     * Scope to get notifications with pending status.
     */
    public function scopePending($query)
    {
        return $query->where('notification_status', NotificationStatus::PENDING);
    }

    /**
     * Scope to get notifications with scheduled status.
     */
    public function scopeScheduled($query)
    {
        return $query->where('notification_status', NotificationStatus::SCHEDULED);
    }

    /**
     * Scope to get notifications with sent status.
     */
    public function scopeSent($query)
    {
        return $query->where('notification_status', NotificationStatus::SENT);
    }

    /**
     * Scope to get notifications with failed status.
     */
    public function scopeFailed($query)
    {
        return $query->where('notification_status', NotificationStatus::FAILED);
    }

    /**
     * Scope to get notifications with processing status.
     */
    public function scopeProcessing($query)
    {
        return $query->where('notification_status', NotificationStatus::PROCESSING);
    }

    /**
     * Scope to get notifications eligible for processing.
     */
    public function scopeEligibleForProcessing($query)
    {
        return $query->where('notification_status', NotificationStatus::PENDING)
            ->where(function ($query) {
                $query->whereNull('scheduled_at')
                    ->orWhere('scheduled_at', '<=', now());
            });
    }

    /**
     * Scope to get notifications by type.
     */
    public function scopeByType($query, NotificationType $type)
    {
        return $query->where('notification_type', $type);
    }

    /**
     * Scope to get notifications by channel.
     */
    public function scopeByChannel($query, NotificationChannel $channel)
    {
        return $query->where('notification_channel', $channel);
    }

    /**
     * Scope to get notifications by recipient.
     */
    public function scopeByRecipient($query, RecipientType $type, int $id)
    {
        return $query->where('recipient_type', $type)
            ->where('recipient_id', $id);
    }

    /**
     * Scope to get notifications by correlation ID.
     */
    public function scopeByCorrelationId($query, string $correlationId)
    {
        return $query->where('correlation_id', $correlationId);
    }

    /**
     * Scope to get notifications that are stuck in scheduled status.
     */
    public function scopeStuckInScheduled($query, int $timeoutMinutes = 30)
    {
        return $query->where('notification_status', NotificationStatus::SCHEDULED)
            ->where('updated_at', '<', now()->subMinutes($timeoutMinutes));
    }

    /**
     * Scope to get notifications for cleanup.
     */
    public function scopeForCleanup($query, int $sentRetentionDays = 30, int $failedRetentionDays = 90)
    {
        return $query->where(function ($query) use ($sentRetentionDays, $failedRetentionDays) {
            $query->where('notification_status', NotificationStatus::SENT)
                ->where('sent_at', '<', now()->subDays($sentRetentionDays));
        })->orWhere(function ($query) use ($failedRetentionDays) {
            $query->where('notification_status', NotificationStatus::FAILED)
                ->where('failed_at', '<', now()->subDays($failedRetentionDays));
        });
    }

    /**
     * Check if the notification is eligible for processing.
     */
    public function isEligibleForProcessing(): bool
    {
        return $this->notification_status->isEligibleForProcessing() &&
            ($this->scheduled_at === null || $this->scheduled_at->isPast());
    }

    /**
     * Check if the notification is pending.
     */
    public function isPending(): bool
    {
        return $this->notification_status === NotificationStatus::PENDING;
    }

    /**
     * Check if the notification is in progress.
     */
    public function isInProgress(): bool
    {
        return $this->notification_status->isInProgress();
    }

    /**
     * Check if the notification is completed.
     */
    public function isCompleted(): bool
    {
        return $this->notification_status->isCompleted();
    }

    /**
     * Check if the notification was sent successfully.
     */
    public function wasSuccessful(): bool
    {
        return $this->notification_status->isSuccessful();
    }

    /**
     * Check if the notification failed.
     */
    public function hasFailed(): bool
    {
        return $this->notification_status->isFailed();
    }

    /**
     * Check if the notification has been read.
     */
    public function hasBeenRead(): bool
    {
        return $this->read_at !== null;
    }

    /**
     * Check if the notification can be retried.
     */
    public function canBeRetried(): bool
    {
        $maxRetries = WalletConfig::get('notifications.retry.max_attempts', 3);

        return $this->notification_status === NotificationStatus::FAILED &&
            $this->retry_count < $maxRetries;
    }

    /**
     * Mark the notification as scheduled.
     */
    public function markAsScheduled(): self
    {
        $this->update([
            'notification_status' => NotificationStatus::SCHEDULED,
            'scheduled_at' => now(),
        ]);

        return $this;
    }

    /**
     * Mark the notification as sent.
     */
    public function markAsSent(): self
    {
        $this->update([
            'notification_status' => NotificationStatus::SENT,
            'sent_at' => now(),
        ]);

        return $this;
    }

    /**
     * Mark the notification as failed.
     */
    public function markAsFailed(string $reason): self
    {
        $this->update([
            'notification_status' => NotificationStatus::FAILED,
            'failed_at' => now(),
            'failed_reason' => $reason,
            'retry_count' => $this->retry_count + 1,
        ]);

        return $this;
    }

    /**
     * Reset the notification for retry.
     */
    public function resetForRetry(): self
    {
        $this->update([
            'notification_status' => NotificationStatus::PENDING,
            'scheduled_at' => null,
        ]);

        return $this;
    }

    /**
     * Mark the notification as pending.
     */
    public function markAsPending(): self
    {
        $this->update([
            'notification_status' => NotificationStatus::PENDING,
            'scheduled_at' => null,
        ]);

        return $this;
    }

    /**
     * Increment the retry count.
     */
    public function incrementRetryCount(): self
    {
        $this->update([
            'retry_count' => $this->retry_count + 1,
        ]);

        return $this;
    }

    /**
     * Mark the notification as read.
     */
    public function markAsRead(): self
    {
        $this->update([
            'read_at' => now(),
        ]);

        return $this;
    }

    /**
     * Get the notification data as an array.
     */
    public function getNotificationData(): array
    {
        return $this->notification_data ?? [];
    }

    /**
     * Set the notification data.
     */
    public function setNotificationData(array $data): self
    {
        $this->notification_data = $data;
        return $this;
    }

    /**
     * Get a specific value from the notification data.
     */
    public function getNotificationDataValue(string $key, $default = null)
    {
        return data_get($this->notification_data, $key, $default);
    }

    /**
     * Set a specific value in the notification data.
     */
    public function setNotificationDataValue(string $key, $value): self
    {
        $data = $this->getNotificationData();
        data_set($data, $key, $value);
        $this->setNotificationData($data);

        return $this;
    }

    /**
     * Get the time elapsed since the notification was created.
     */
    public function getElapsedTime(): Carbon
    {
        return $this->created_at->diffForHumans();
    }

    /**
     * Get the processing time if the notification was sent.
     */
    public function getProcessingTime(): ?int
    {
        if ($this->sent_at && $this->created_at) {
            return $this->created_at->diffInSeconds($this->sent_at);
        }

        return null;
    }

    /**
     * Get the priority level of this notification.
     */
    public function getPriority(): string
    {
        return $this->notification_type->getPriority();
    }

    /**
     * Get the admin role that should receive this notification.
     */
    public function getAdminRole(): string
    {
        return $this->notification_type->getAdminRole();
    }

    /**
     * Get the category of this notification.
     */
    public function getCategory(): string
    {
        return $this->notification_type->getCategory();
    }

    /**
     * Create a new factory instance for the model.
     */
    protected static function newFactory()
    {
        return WalletNotificationFactory::new();
    }
}