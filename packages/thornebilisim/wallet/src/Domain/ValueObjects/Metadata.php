<?php

declare(strict_types=1);

namespace Thorne\Wallet\Domain\ValueObjects;

use Carbon\Carbon;
use JsonSerializable;
use Thorne\Wallet\Domain\Exceptions\InvalidMetadataException;

/**
 * Metadata Value Object.
 *
 * Standardized metadata for all domain events providing audit trail and context.
 * Automatically captures HTTP request context or system operation details.
 */
final class Metadata implements JsonSerializable
{
    private ?array $performedBy;
    private ?string $ipAddress;
    private ?string $userAgent;
    private ?string $url;
    private Carbon $occurredAt;
    private array $additionalData;

    public function __construct(
        ?array $performedBy = null,
        ?string $ipAddress = null,
        ?string $userAgent = null,
        ?string $url = null,
        ?Carbon $occurredAt = null,
        array $additionalData = []
    ) {
        $this->performedBy = $this->validatePerformedBy($performedBy);
        $this->ipAddress = $this->validateIpAddress($ipAddress);
        $this->userAgent = $userAgent;
        $this->url = $this->validateUrl($url);
        $this->occurredAt = $occurredAt ?? Carbon::now();
        $this->additionalData = $additionalData;
    }

    /**
     * Create metadata from HTTP request context.
     * Note: This is automatically called by WalletMetadataMiddleware.
     */
    public static function fromRequest(): self
    {
        $userId = auth()->id();
        return new self(
            performedBy: $userId ? ['type' => 'user', 'id' => (string) $userId] : null,
            ipAddress: request()->ip(),
            userAgent: request()->userAgent(),
            url: request()->fullUrl(),
            occurredAt: Carbon::now(),
            additionalData: ['source' => 'http']
        );
    }

    /**
     * Create metadata for system operations (commands, jobs, observers, sagas).
     * 
     * @param string $context The system context (COMMAND, JOB, OBSERVER, SAGA, SCHEDULER)
     * @param array $additionalData Context-specific data
     */
    public static function fromSystem(string $context, array $additionalData = []): self
    {
        return new self(
            performedBy: ['type' => 'system', 'id' => strtoupper($context)],
            occurredAt: Carbon::now(),
            additionalData: array_merge(['source' => 'system', 'context' => $context], $additionalData)
        );
    }

    /**
     * Create metadata manually (for testing or debugging).
     */
    public static function manual(array $data = []): self
    {
        return new self(
            performedBy: $data['performed_by'] ?? ['type' => 'manual', 'id' => 'MANUAL'],
            ipAddress: $data['ip_address'] ?? null,
            userAgent: $data['user_agent'] ?? null,
            url: $data['url'] ?? null,
            occurredAt: isset($data['occurred_at']) ? Carbon::parse($data['occurred_at']) : Carbon::now(),
            additionalData: array_merge(['source' => 'manual'], $data['additional_data'] ?? [])
        );
    }

    /**
     * Get who performed the action.
     */
    public function getPerformedBy(): ?array
    {
        return $this->performedBy;
    }

    /**
     * Get IP address.
     */
    public function getIpAddress(): ?string
    {
        return $this->ipAddress;
    }

    /**
     * Get user agent.
     */
    public function getUserAgent(): ?string
    {
        return $this->userAgent;
    }

    /**
     * Get URL.
     */
    public function getUrl(): ?string
    {
        return $this->url;
    }

    /**
     * Get occurrence timestamp.
     */
    public function getOccurredAt(): Carbon
    {
        return $this->occurredAt;
    }

    /**
     * Get additional data.
     */
    public function getAdditionalData(): array
    {
        return $this->additionalData;
    }

    /**
     * Create new instance with additional data.
     */
    public function withAdditionalData(array $data): self
    {
        return new self(
            $this->performedBy,
            $this->ipAddress,
            $this->userAgent,
            $this->url,
            $this->occurredAt,
            array_merge($this->additionalData, $data)
        );
    }

    /**
     * Convert to array.
     */
    public function toArray(): array
    {
        return [
            'performed_by' => $this->performedBy,
            'ip_address' => $this->ipAddress,
            'user_agent' => $this->userAgent,
            'url' => $this->url,
            'occurred_at' => $this->occurredAt->toISOString(),
            'additional_data' => $this->additionalData,
        ];
    }

    /**
     * JSON serialization.
     */
    public function jsonSerialize(): array
    {
        return $this->toArray();
    }

    /**
     * Validate IP address.
     */
    private function validateIpAddress(?string $ipAddress): ?string
    {
        if ($ipAddress === null) {
            return null;
        }

        if (!filter_var($ipAddress, FILTER_VALIDATE_IP)) {
            throw InvalidMetadataException::invalidIpAddress($ipAddress);
        }

        return $ipAddress;
    }

    /**
     * Validate URL.
     */
    private function validateUrl(?string $url): ?string
    {
        if ($url === null) {
            return null;
        }

        if (!filter_var($url, FILTER_VALIDATE_URL)) {
            throw InvalidMetadataException::invalidUrl($url);
        }

        return $url;
    }

    /**
     * Validate performedBy structure.
     */
    private function validatePerformedBy(?array $performedBy): ?array
    {
        if ($performedBy === null) {
            return null;
        }

        if (!isset($performedBy['type']) || !isset($performedBy['id'])) {
            throw InvalidMetadataException::invalidPerformedBy(
                "performedBy must have 'type' and 'id' keys",
                $performedBy
            );
        }

        if (!is_string($performedBy['type']) || !is_string($performedBy['id'])) {
            throw InvalidMetadataException::invalidPerformedBy(
                "performedBy 'type' and 'id' must be strings",
                $performedBy
            );
        }

        $validTypes = ['user', 'system', 'manual'];
        if (!in_array($performedBy['type'], $validTypes)) {
            throw InvalidMetadataException::invalidPerformedBy(
                "performedBy 'type' must be one of: " . implode(', ', $validTypes),
                $performedBy
            );
        }

        return $performedBy;
    }
}
