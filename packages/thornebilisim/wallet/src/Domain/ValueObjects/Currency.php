<?php

declare(strict_types=1);

namespace Thorne\Wallet\Domain\ValueObjects;

use JsonSerializable;
use Thorne\Wallet\Domain\Exceptions\UnsupportedCurrencyException;
use Thorne\Wallet\Support\Helpers\WalletConfig;

/**
 * Currency Value Object.
 *
 * Represents supported currencies with validation and configuration access.
 * Provides type-safe access to currency properties and validation methods.
 */
final class Currency implements JsonSerializable
{
    private string $code;
    private array $config;

    private function __construct(string $code, array $config)
    {
        $this->code = strtoupper($code);
        $this->config = $config;
    }

    /**
     * Create Currency from code.
     */
    public static function from(string $code): self
    {
        $code = strtoupper($code);
        
        if (!WalletConfig::isCurrencySupported($code)) {
            throw UnsupportedCurrencyException::notSupported($code);
        }

        $config = WalletConfig::getCurrency($code);
        
        if ($config === null) {
            throw UnsupportedCurrencyException::notFound($code);
        }

        return new self($code, $config);
    }

    /**
     * Get the default system currency.
     */
    public static function getDefault(): self
    {
        $defaultCurrency = WalletConfig::getDefaultCurrency();
        
        if ($defaultCurrency === null) {
            throw UnsupportedCurrencyException::notConfigured('default', 'No default currency configured');
        }

        return self::from($defaultCurrency['code']);
    }

    /**
     * Get all supported currencies as Currency objects.
     */
    public static function getAllSupported(): array
    {
        return array_map(
            fn($code) => self::from($code),
            WalletConfig::getSupportedCurrencies()
        );
    }

    /**
     * Get currency code.
     */
    public function getCode(): string
    {
        return $this->code;
    }

    /**
     * Get currency name.
     */
    public function getName(): string
    {
        return $this->config['name'] ?? $this->code;
    }

    /**
     * Get currency symbol.
     */
    public function getSymbol(): string
    {
        return $this->config['symbol'] ?? $this->code;
    }

    /**
     * Get ISO numeric code.
     */
    public function getIsoCode(): int
    {
        return (int) ($this->config['iso_code'] ?? 0);
    }

    /**
     * Get currency precision (decimal places).
     */
    public function getPrecision(): int
    {
        return (int) ($this->config['precision'] ?? 2);
    }

    /**
     * Check if currency is enabled.
     */
    public function isEnabled(): bool
    {
        return (bool) ($this->config['enabled'] ?? false);
    }

    /**
     * Check if this is the default currency.
     */
    public function isDefault(): bool
    {
        return (bool) ($this->config['is_default'] ?? false);
    }

    /**
     * Check if this is a custom currency.
     */
    public function isCustom(): bool
    {
        return (bool) ($this->config['is_custom'] ?? false);
    }

    /**
     * Check if currency is supported.
     */
    public function isSupported(): bool
    {
        return WalletConfig::isCurrencySupported($this->code);
    }

    /**
     * Check equality with another Currency.
     */
    public function equals(Currency $other): bool
    {
        return $this->code === $other->code;
    }

    /**
     * Convert to array.
     */
    public function toArray(): array
    {
        return [
            'code' => $this->code,
            'name' => $this->getName(),
            'symbol' => $this->getSymbol(),
            'iso_code' => $this->getIsoCode(),
            'precision' => $this->getPrecision(),
            'enabled' => $this->isEnabled(),
            'is_default' => $this->isDefault(),
            'is_custom' => $this->isCustom(),
        ];
    }

    /**
     * Convert to string representation.
     */
    public function toString(): string
    {
        return $this->code;
    }

    /**
     * String representation.
     */
    public function __toString(): string
    {
        return $this->toString();
    }

    /**
     * JSON serialization.
     */
    public function jsonSerialize(): array
    {
        return $this->toArray();
    }
}
