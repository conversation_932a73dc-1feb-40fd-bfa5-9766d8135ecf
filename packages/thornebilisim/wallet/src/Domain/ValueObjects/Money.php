<?php

declare(strict_types=1);

namespace Thorne\Wallet\Domain\ValueObjects;

use Brick\Money\Money as BrickMoney;
use Brick\Money\Context\CustomContext;
use JsonSerializable;
use Thorne\Wallet\Domain\Exceptions\InvalidMoneyException;
use Thorne\Wallet\Support\Helpers\WalletConfig;

/**
 * Money Value Object.
 *
 * Lightweight wrapper around brick/money for wallet system constraints.
 * Uses DECIMAL(20,8) precision for accurate financial calculations.
 */
final class Money implements JsonSerializable
{
    private BrickMoney $money;
    private const PRECISION = 8;
    private const SCALE = 8;

    private function __construct(BrickMoney $money)
    {
        $this->money = $money;
    }

    /**
     * Create Money instance from amount and currency.
     */
    public static function of(string|int|float $amount, string $currency): self
    {
        if (!WalletConfig::isCurrencySupported($currency)) {
            throw InvalidMoneyException::unsupportedCurrency($currency);
        }

        $context = new CustomContext(self::PRECISION);
        return new self(BrickMoney::of($amount, $currency, $context));
    }

    /**
     * Create zero amount for given currency.
     */
    public static function zero(string $currency): self
    {
        return self::of(0, $currency);
    }

    /**
     * Get the amount as string with full precision.
     */
    public function getAmount(): string
    {
        return $this->money->getAmount()->toScale(self::SCALE)->__toString();
    }

    /**
     * Get the currency code.
     */
    public function getCurrency(): string
    {
        return $this->money->getCurrency()->getCurrencyCode();
    }

    /**
     * Add another Money amount.
     */
    public function add(Money $other): self
    {
        return new self($this->money->plus($other->money));
    }

    /**
     * Subtract another Money amount.
     */
    public function subtract(Money $other): self
    {
        return new self($this->money->minus($other->money));
    }

    /**
     * Multiply by a factor.
     */
    public function multiply(string|int|float $multiplier): self
    {
        return new self($this->money->multipliedBy($multiplier));
    }

    /**
     * Divide by a divisor.
     */
    public function divide(string|int|float $divisor): self
    {
        return new self($this->money->dividedBy($divisor));
    }

    /**
     * Check if amount is positive.
     */
    public function isPositive(): bool
    {
        return $this->money->isPositive();
    }

    /**
     * Check if amount is negative.
     */
    public function isNegative(): bool
    {
        return $this->money->isNegative();
    }

    /**
     * Check if amount is zero.
     */
    public function isZero(): bool
    {
        return $this->money->isZero();
    }

    /**
     * Check if this amount is greater than another.
     */
    public function isGreaterThan(Money $other): bool
    {
        return $this->money->isGreaterThan($other->money);
    }

    /**
     * Check if this amount is less than another.
     */
    public function isLessThan(Money $other): bool
    {
        return $this->money->isLessThan($other->money);
    }

    /**
     * Expose underlying brick/money for advanced operations.
     */
    public function toBrickMoney(): BrickMoney
    {
        return $this->money;
    }

    /**
     * Check equality with another Money instance.
     */
    public function equals(Money $other): bool
    {
        return $this->money->isEqualTo($other->money);
    }

    /**
     * Convert to array.
     */
    public function toArray(): array
    {
        return [
            'amount' => $this->getAmount(),
            'currency' => $this->getCurrency(),
        ];
    }

    /**
     * Convert to string representation.
     */
    public function toString(): string
    {
        return $this->getAmount() . ' ' . $this->getCurrency();
    }

    /**
     * String representation.
     */
    public function __toString(): string
    {
        return $this->toString();
    }

    /**
     * JSON serialization.
     */
    public function jsonSerialize(): array
    {
        return $this->toArray();
    }
}
