<?php

declare(strict_types=1);

namespace Thorne\Wallet\Domain\ValueObjects;

use JsonSerializable;
use Thorne\Wallet\Domain\Exceptions\InvalidAccountNumberException;
use Thorne\Wallet\Support\Helpers\WalletConfig;

/**
 * AccountNumber Value Object.
 *
 * Generates and validates 11-digit account numbers using the <PERSON>hn algorithm.
 * Format: 3-digit currency ISO code + 7 random digits + 1 <PERSON>hn check digit.
 */
final class AccountNumber implements JsonSerializable
{
    private string $number;
    private const LENGTH = 11;

    private function __construct(string $number)
    {
        $this->validateFormat($number);
        $this->validateChecksum($number);
        $this->number = $number;
    }

    /**
     * Generate new account number for given currency.
     */
    public static function generate(string $currencyCode): self
    {
        try {
            $currency = Currency::from($currencyCode);
            $isoCode = str_pad((string) $currency->getIsoCode(), 3, '0', STR_PAD_LEFT);

            // Generate 7 random digits
            $randomPart = str_pad((string) random_int(1000000, 9999999), 7, '0', STR_PAD_LEFT);

            // Calculate <PERSON>hn check digit
            $baseNumber = $isoCode . $randomPart;
            $checkDigit = self::calculateLuhnCheckDigit($baseNumber);

            $accountNumber = $baseNumber . $checkDigit;

            return new self($accountNumber);
        } catch (\Exception $e) {
            throw InvalidAccountNumberException::generationFailed($e->getMessage());
        }
    }

    /**
     * Create AccountNumber from string.
     */
    public static function fromString(string $number): self
    {
        // Remove any formatting
        $number = preg_replace('/[^0-9]/', '', $number);

        return new self($number);
    }

    /**
     * Get the full account number.
     */
    public function getNumber(): string
    {
        return $this->number;
    }

    /**
     * Get the currency ISO code part.
     */
    public function getCurrencyIsoCode(): string
    {
        return substr($this->number, 0, 3);
    }

    /**
     * Get the sequence number part.
     */
    public function getSequenceNumber(): string
    {
        return substr($this->number, 3, 7);
    }

    /**
     * Get the check digit.
     */
    public function getCheckDigit(): string
    {
        return substr($this->number, -1);
    }

    /**
     * Get account type based on currency.
     */
    public function getAccountType(): string
    {
        $currency = $this->getCurrency();
        return $currency->isCustom() ? 'custom' : 'standard';
    }

    /**
     * Check if account belongs to a specific currency.
     */
    public function belongsToCurrency(string $currencyCode): bool
    {
        return strtoupper($currencyCode) === $this->getCurrency()->getCode();
    }

    /**
     * Get a masked version of the account number for display.
     */
    public function getMasked(): string
    {
        $iso = $this->getCurrencyIsoCode();
        $sequence = $this->getSequenceNumber();
        $check = $this->getCheckDigit();

        // Show first 3 and last 1, mask middle with asterisks
        return $iso . '-' . str_repeat('*', 4) . substr($sequence, -3) . '-' . $check;
    }

    /**
     * Validate if this account number could be valid for a specific currency.
     */
    public function isValidForCurrency(string $currencyCode): bool
    {
        try {
            $currency = Currency::from($currencyCode);
            $expectedIsoCode = str_pad((string) $currency->getIsoCode(), 3, '0', STR_PAD_LEFT);
            return $this->getCurrencyIsoCode() === $expectedIsoCode;
        } catch (\Exception) {
            return false;
        }
    }

    /**
     * Get formatted account number (XXX-XXXXXXX-X).
     */
    public function getFormatted(): string
    {
        return substr($this->number, 0, 3) . '-' .
               substr($this->number, 3, 7) . '-' .
               substr($this->number, -1);
    }

    /**
     * Get the Currency object for this account.
     */
    public function getCurrency(): Currency
    {
        $isoCode = (int) $this->getCurrencyIsoCode();

        $supportedCurrencies = WalletConfig::getAllCurrencies();
        foreach ($supportedCurrencies as $code => $config) {
            if (($config['iso_code'] ?? 0) === $isoCode) {
                return Currency::from($code);
            }
        }

        throw InvalidAccountNumberException::invalidCurrencyCode((string) $isoCode);
    }

    /**
     * Validate the account number.
     */
    public function isValid(): bool
    {
        try {
            $this->validateFormat($this->number);
            $this->validateChecksum($this->number);
            return true;
        } catch (InvalidAccountNumberException) {
            return false;
        }
    }

    /**
     * Check equality with another AccountNumber.
     */
    public function equals(AccountNumber $other): bool
    {
        return $this->number === $other->number;
    }

    /**
     * Convert to array.
     */
    public function toArray(): array
    {
        return [
            'number' => $this->number,
            'formatted' => $this->getFormatted(),
            'masked' => $this->getMasked(),
            'currency_iso_code' => $this->getCurrencyIsoCode(),
            'sequence_number' => $this->getSequenceNumber(),
            'check_digit' => $this->getCheckDigit(),
            'currency_code' => $this->getCurrency()->getCode(),
            'account_type' => $this->getAccountType(),
        ];
    }

    /**
     * Convert to string representation.
     */
    public function toString(): string
    {
        return $this->number;
    }

    /**
     * String representation.
     */
    public function __toString(): string
    {
        return $this->toString();
    }

    /**
     * JSON serialization.
     */
    public function jsonSerialize(): array
    {
        return $this->toArray();
    }

    /**
     * Validate account number format.
     */
    private function validateFormat(string $number): void
    {
        if (!ctype_digit($number)) {
            throw InvalidAccountNumberException::invalidFormat($number, 'must contain only digits');
        }

        if (strlen($number) !== self::LENGTH) {
            throw InvalidAccountNumberException::invalidLength($number, self::LENGTH);
        }
    }

    /**
     * Validate Luhn checksum.
     */
    private function validateChecksum(string $number): void
    {
        $baseNumber = substr($number, 0, -1);
        $providedCheckDigit = (int) substr($number, -1);
        $calculatedCheckDigit = self::calculateLuhnCheckDigit($baseNumber);

        if ($providedCheckDigit !== $calculatedCheckDigit) {
            throw InvalidAccountNumberException::invalidChecksum($number);
        }
    }

    /**
     * Calculate Luhn check digit.
     */
    private static function calculateLuhnCheckDigit(string $number): int
    {
        $sum = 0;
        $length = strlen($number);

        for ($i = 0; $i < $length; $i++) {
            $digit = (int) $number[$length - 1 - $i];

            if ($i % 2 === 0) { // Even position (from right)
                $digit *= 2;
                if ($digit > 9) {
                    $digit -= 9;
                }
            }

            $sum += $digit;
        }

        return (10 - ($sum % 10)) % 10;
    }
}
