<?php

declare(strict_types=1);

namespace Thorne\Wallet\Application\Console\Commands;

use Illuminate\Console\Command;
use Thorne\Wallet\Application\Services\WalletNotificationService;

class WalletNotificationsPublishCommand extends Command
{
    protected $signature = 'wallet:notifications:publish
                            {--batch-size=100 : Number of notifications to process per batch}
                            {--delay=2 : Delay in seconds between each notification dispatch}
                            {--scheduled : Also process scheduled notifications}
                            {--retry-failed : Also retry failed notifications}';

    protected $description = 'Process notifications';

    public function __construct(
        private readonly WalletNotificationService $notificationService
    ) {
        parent::__construct();
    }

    public function handle(): int
    {
        $batchSize = (int) $this->option('batch-size');
        $delay = max(1, (int) $this->option('delay')); // Minimum 1 second
        $processScheduled = $this->option('scheduled');
        $retryFailed = $this->option('retry-failed');

        $this->info('🚀 Starting notification processing...');
        $this->newLine();
        $this->info("Batch size: {$batchSize}");
        $this->info("Delay between notifications: {$delay}s");
        $this->info('Process scheduled: ' . ($processScheduled ? 'Yes' : 'No'));
        $this->info('Retry failed: ' . ($retryFailed ? 'Yes' : 'No'));
        $this->newLine();

        $totalDispatched = 0;

        try {
            // Process pending notifications
            $this->info('📤 Processing pending notifications...');
            $pendingResults = $this->notificationService->processPending($batchSize, $delay);
            $this->displayResults('Pending', $pendingResults);
            $totalDispatched += $pendingResults['dispatched'];

            // Process scheduled notifications
            if ($processScheduled) {
                $this->info('⏰ Processing scheduled notifications...');
                $scheduledResults = $this->notificationService->processScheduled($batchSize, $delay);
                $this->displayResults('Scheduled', $scheduledResults);
                $totalDispatched += $scheduledResults['dispatched'];
            }

            // Retry failed notifications
            if ($retryFailed) {
                $this->info('🔄 Retrying failed notifications...');
                $retryLimit = (int) ($batchSize / 2); // Use half the batch size for retries
                $retryResults = $this->notificationService->retryFailed($retryLimit, $delay);
                $this->displayRetryResults($retryResults);
                $totalDispatched += $retryResults['dispatched'];
            }

            $this->newLine();
            if ($totalDispatched > 0) {
                $totalTime = $totalDispatched * $delay;
                $this->info("✅ Total notifications dispatched: {$totalDispatched}");
                $this->line("⏱️  Estimated completion time: {$totalTime} seconds (" . gmdate('H:i:s', $totalTime) . ")");
                $this->line("👀 Monitor progress in Horizon dashboard");
            } else {
                $this->info("ℹ️  No notifications to process");
            }

        } catch (\Exception $e) {
            $this->error('❌ Error during notification processing: ' . $e->getMessage());
            return 1;
        }

        return 0;
    }

    private function displayResults(string $type, array $results): void
    {
        $processed = $results['processed'] ?? 0;
        $dispatched = $results['dispatched'] ?? 0;

        if ($processed > 0) {
            $this->line("  • {$type}: {$processed} processed, {$dispatched} dispatched to queue");
        } else {
            $this->line("  • {$type}: No notifications to process");
        }
    }

    private function displayRetryResults(array $results): void
    {
        $retried = $results['retried'] ?? 0;
        $dispatched = $results['dispatched'] ?? 0;

        if ($retried > 0) {
            $this->line("  • Retry: {$retried} prepared for retry, {$dispatched} dispatched to queue");
        } else {
            $this->line("  • Retry: No failed notifications to retry");
        }
    }
}