<?php

declare(strict_types=1);

namespace Thorne\Wallet\Application\Console\Commands;

use Illuminate\Console\Command;
use Thorne\Wallet\Application\Jobs\SendWalletNotificationBatchJob;
use Thorne\Wallet\Application\Jobs\SendWalletNotificationJob;
use Thorne\Wallet\Domain\Models\WalletNotification;

class WalletNotificationsRetryCommand extends Command
{
    protected $signature = 'wallet:notifications:retry
                            {--id= : Specific notification ID to retry (single mode)}
                            {--limit=50 : Maximum number of failed notifications to retry (bulk mode)}
                            {--force : Force retry even if max retry count exceeded}
                            {--delay=2 : Delay in seconds between each notification dispatch}
                            {--dry-run : Show what would be retried without actually doing it}';

    protected $description = 'Retry failed wallet notifications';

    public function handle(): int
    {
        $notificationId = $this->option('id');
        $limit = (int) $this->option('limit');
        $force = $this->option('force');
        $delay = max(1, (int) $this->option('delay')); // Minimum 1 second
        $dryRun = $this->option('dry-run');

        // Single notification mode
        if ($notificationId) {
            return $this->retrySingle($notificationId, $force, $dryRun);
        }

        // Bulk notification mode
        $this->info("Finding failed notifications to retry...");

        $query = WalletNotification::failed();

        if (!$force) {
            $query->where('retry_count', '<', 3);
        }

        $notifications = $query->limit($limit)->get();

        if ($notifications->isEmpty()) {
            $this->info("No failed notifications found to retry");
            return 0;
        }

        $this->line("Found {$notifications->count()} failed notifications");

        if ($dryRun) {
            $this->table(
                ['ID', 'Type', 'Channel', 'Recipient', 'Retry Count', 'Failed At'],
                $notifications->map(function ($notification) {
                    return [
                        $notification->id,
                        $notification->notification_type->value,
                        $notification->notification_channel->value,
                        "{$notification->recipient_type->value} #{$notification->recipient_id}",
                        $notification->retry_count,
                        $notification->failed_at?->format('Y-m-d H:i:s'),
                    ];
                })->toArray()
            );

            $totalTime = $notifications->count() * $delay;
            $this->info("DRY RUN: Would retry {$notifications->count()} notifications with {$delay}s delays");
            $this->line("⏱️  Estimated completion time: {$totalTime} seconds (" . gmdate('H:i:s', $totalTime) . ")");
            return 0;
        }

        if (!$this->confirm("Do you want to retry {$notifications->count()} failed notifications with {$delay}s delays?")) {
            return 0;
        }

        $this->info("Preparing notifications for batch retry...");
        $bar = $this->output->createProgressBar($notifications->count());
        $bar->start();

        $prepared = 0;
        $errors = 0;
        $notificationIds = collect();

        foreach ($notifications as $notification) {
            try {
                $notification->incrementRetryCount();
                $notification->markAsPending();
                $notificationIds->push($notification->id);
                $prepared++;
            } catch (\Exception $e) {
                $this->error("\nFailed to prepare notification {$notification->id}: " . $e->getMessage());
                $errors++;
            }

            $bar->advance();
        }

        $bar->finish();
        $this->newLine();

        if ($prepared > 0) {
            dispatch(new SendWalletNotificationBatchJob($notificationIds, $delay));

            $this->info("✅ Prepared {$prepared} notifications for batch retry");
            $this->info("📡 Dispatched batch job with {$delay}s delay between notifications");

            $totalTime = $prepared * $delay;
            $this->line("⏱️  Estimated completion time: {$totalTime} seconds (" . gmdate('H:i:s', $totalTime) . ")");
            $this->line("👀 Monitor progress in Horizon dashboard");
        }

        if ($errors > 0) {
            $this->warn("⚠️  {$errors} notifications failed to prepare");
        }

        return $errors > 0 ? 1 : 0;
    }

    private function retrySingle(string $notificationId, bool $force, bool $dryRun): int
    {
        $notification = WalletNotification::find($notificationId);

        if (!$notification) {
            $this->error("Notification with ID {$notificationId} not found");
            return 1;
        }

        if (!$notification->hasFailed()) {
            $this->error("Notification {$notificationId} is not in FAILED status (current: {$notification->notification_status->value})");
            return 1;
        }

        $this->line("Notification Details:");
        $this->line("  ID: {$notification->id}");
        $this->line("  Type: {$notification->notification_type->value}");
        $this->line("  Channel: {$notification->notification_channel->value}");
        $this->line("  Recipient: {$notification->recipient_type->value} #{$notification->recipient_id}");
        $this->line("  Retry Count: {$notification->retry_count}");
        $this->line("  Failed At: {$notification->failed_at}");
        $this->line("  Error: " . ($notification->error ?? 'Unknown error'));

        if (!$force && $notification->retry_count >= 3) {
            $this->warn("Notification has exceeded max retry count ({$notification->retry_count}/3)");
            if (!$this->confirm('Do you want to retry anyway?')) {
                return 0;
            }
        }

        if ($dryRun) {
            $this->info("DRY RUN: Would retry notification {$notification->id}");
            return 0;
        }

        try {
            $notification->incrementRetryCount();
            $notification->markAsPending();

            // Dispatch the job again
            dispatch(new SendWalletNotificationJob($notification));

            $this->info("✅ Notification {$notification->id} has been marked as PENDING and queued for retry");
            return 0;
        } catch (\Exception $e) {
            $this->error("Failed to retry notification: " . $e->getMessage());
            return 1;
        }
    }
}