<?php

declare(strict_types=1);

namespace Thorne\Wallet\Application\Services;

use Carbon\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Thorne\Wallet\Support\Facades\WalletLog;
use Thorne\Wallet\Support\Helpers\WalletConfig;
use Thorne\Wallet\Application\Jobs\SendWalletNotificationJob;
use Thorne\Wallet\Domain\Enums\NotificationChannel;
use Thorne\Wallet\Domain\Enums\NotificationStatus;
use Thorne\Wallet\Domain\Enums\NotificationType;
use Thorne\Wallet\Domain\Enums\RecipientType;
use Thorne\Wallet\Domain\Models\WalletNotification;
use Thorne\Wallet\Domain\Services\NotificationTemplateService;
use Thorne\Wallet\Domain\Exceptions\WalletNotificationException;
use Thorne\Wallet\Infrastructure\Notifications\Channels\WalletAlertChannel;
use Thorne\Wallet\Infrastructure\Notifications\Channels\WalletEmailChannel;
use Thorne\Wallet\Infrastructure\Notifications\Channels\WalletPushChannel;
use Thorne\Wallet\Infrastructure\Notifications\Channels\WalletSmsChannel;

class WalletNotificationService
{
    private array $channels = [];

    public function __construct(
        private readonly NotificationTemplateService $templateService,
        private readonly WalletEmailChannel $emailChannel,
        private readonly WalletSmsChannel $smsChannel,
        private readonly WalletPushChannel $pushChannel,
        private readonly WalletAlertChannel $alertChannel
    ) {
        $this->channels = [
            NotificationChannel::EMAIL->value => $this->emailChannel,
            NotificationChannel::SMS->value => $this->smsChannel,
            NotificationChannel::PUSH->value => $this->pushChannel,
            NotificationChannel::ALERT->value => $this->alertChannel,
        ];
    }

    public function create(
        NotificationType $type,
        NotificationChannel $channel,
        RecipientType $recipientType,
        int $recipientId,
        array $data = [],
        ?string $locale = null,
        ?\DateTimeInterface $scheduledAt = null
    ): WalletNotification {
        if (!WalletConfig::get('notifications.enabled', true)) {
            throw WalletNotificationException::disabled();
        }

        // If locale not provided, try to get from recipient
        if (!$locale) {
            $locale = $this->getRecipientLocale($recipientType, $recipientId);
        }

        $notification = WalletNotification::create([
            'notification_type' => $type->value,
            'notification_channel' => $channel->value,
            'recipient_type' => $recipientType->value,
            'recipient_id' => $recipientId,
            'notification_data' => $data,
            'language' => $locale ?? app()->getLocale(),
            'notification_status' => $scheduledAt ? NotificationStatus::SCHEDULED->value : NotificationStatus::PENDING->value,
            'scheduled_at' => $scheduledAt?->format('Y-m-d H:i:s'),
            'retry_count' => 0,
        ]);

        if (!$scheduledAt) {
            dispatch(new SendWalletNotificationJob($notification));
        }

        return $notification;
    }

    public function createBulk(
        NotificationType $type,
        NotificationChannel $channel,
        RecipientType $recipientType,
        array $recipientIds,
        array $data = [],
        ?string $locale = null,
        ?\DateTimeInterface $scheduledAt = null
    ): Collection {
        if (!WalletConfig::get('notifications.enabled', true)) {
            throw WalletNotificationException::disabled();
        }

        $notifications = collect();
        $now = Carbon::now();
        $status = $scheduledAt ? NotificationStatus::SCHEDULED->value : NotificationStatus::PENDING->value;

        DB::transaction(function () use ($type, $channel, $recipientType, $recipientIds, $data, $locale, $status, $scheduledAt, $now, &$notifications) {
            $records = [];

            foreach (array_unique($recipientIds) as $recipientId) {
                // Get recipient-specific locale if not provided
                $recipientLocale = $locale ?: $this->getRecipientLocale($recipientType, $recipientId);
                
                $records[] = [
                    'uuid' => WalletNotification::newUuid(),
                    'notification_type' => $type->value,
                    'notification_channel' => $channel->value,
                    'recipient_type' => $recipientType->value,
                    'recipient_id' => $recipientId,
                    'notification_data' => json_encode($data),
                    'language' => $recipientLocale ?? app()->getLocale(),
                    'notification_status' => $status,
                    'scheduled_at' => $scheduledAt?->format('Y-m-d H:i:s'),
                    'retry_count' => 0,
                    'created_at' => $now,
                    'updated_at' => $now,
                ];
            }

            WalletNotification::insert($records);

            $notifications = WalletNotification::whereIn('recipient_id', $recipientIds)
                ->where('notification_type', $type->value)
                ->where('notification_channel', $channel->value)
                ->where('created_at', '>=', $now->subSecond())
                ->get();
        });

        if (!$scheduledAt) {
            foreach ($notifications as $notification) {
                dispatch(new SendWalletNotificationJob($notification));
            }
        }

        return $notifications;
    }

    public function send(WalletNotification $notification): bool
    {
        if (!WalletConfig::get('notifications.enabled', true)) {
            return false;
        }

        $channelConfig = WalletConfig::get("notifications.channels.{$notification->notification_channel->getConfigKey()}", []);
        if (!($channelConfig['enabled'] ?? true)) {
            WalletLog::info()
                ->message("Notification channel {$notification->notification_channel->value} is disabled")
                ->context([
                    'notification_id' => $notification->id,
                ])
                ->write();
            return false;
        }

        try {
            $channel = $this->channels[$notification->notification_channel->value] ?? null;
            if (!$channel) {
                throw WalletNotificationException::channelNotConfigured($notification->notification_channel->value);
            }

            $recipient = $this->resolveRecipient($notification);
            if (!$recipient) {
                throw WalletNotificationException::recipientNotFound(
                    $notification->recipient_id,
                    $notification->recipient_type->value
                );
            }

            $content = $this->templateService->render(
                $notification->notification_type,
                $notification->notification_channel,
                $notification->language,
                array_merge($notification->notification_data, [
                    'recipient' => $recipient,
                    'notification' => $notification,
                ])
            );

            $result = $channel->send($recipient, $content, $notification);

            if ($result) {
                $this->markAsSent($notification, ['sent_via' => $notification->notification_channel->value]);
            } else {
                $this->markAsFailed($notification, 'Channel returned false', []);
            }

            return $result;
        } catch (WalletNotificationException $e) {
            WalletLog::error()
                ->message('Notification error: ' . $e->getMessage())
                ->context([
                    'notification_id' => $notification->id,
                    'error' => $e->getMessage(),
                ])
                ->write();

            $this->markAsFailed($notification, $e->getMessage(), [
                'exception' => get_class($e),
            ]);

            return false;
        } catch (\Exception $e) {
            WalletLog::error()
                ->message('Unexpected error sending notification')
                ->context([
                    'notification_id' => $notification->id,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                ])
                ->write();

            $this->markAsFailed($notification, $e->getMessage(), [
                'exception' => get_class($e),
            ]);

            return false;
        }
    }

    public function sendBatch(Collection $notifications, int $delayBetweenJobs = 2): array
    {
        if ($notifications->isEmpty()) {
            return ['dispatched' => 0];
        }

        // Dispatch batch job for rate-limited processing
        dispatch(new \Thorne\Wallet\Application\Jobs\SendWalletNotificationBatchJob(
            $notifications->pluck('id'),
            $delayBetweenJobs
        ));

        WalletLog::info()
            ->message('Dispatched batch notification job')
            ->context([
                'notification_count' => $notifications->count(),
                'delay_between_jobs' => $delayBetweenJobs,
            ])
            ->write();

        return ['dispatched' => $notifications->count()];
    }

    public function processPending(int $limit = 100, int $delayBetweenJobs = 2): array
    {
        $notifications = $this->getByStatus(NotificationStatus::PENDING, $limit);
        $results = $this->sendBatch($notifications, $delayBetweenJobs);

        return [
            'processed' => $notifications->count(),
            'dispatched' => $results['dispatched'],
        ];
    }

    public function processScheduled(int $limit = 100, int $delayBetweenJobs = 2): array
    {
        $notifications = WalletNotification::scheduled()
            ->where('scheduled_at', '<=', Carbon::now())
            ->limit($limit)
            ->get();

        foreach ($notifications as $notification) {
            $notification->markAsPending();
        }

        $results = $this->sendBatch($notifications, $delayBetweenJobs);

        return [
            'processed' => $notifications->count(),
            'dispatched' => $results['dispatched'],
        ];
    }

    public function retryFailed(int $limit = 50, int $delayBetweenJobs = 2): array
    {
        $maxRetries = WalletConfig::get('notifications.retry.max_attempts', 3);

        $notifications = WalletNotification::failed()
            ->where('retry_count', '<', $maxRetries)
            ->whereNotNull('failed_at')
            ->where('failed_at', '<=', Carbon::now()->subMinutes(
                WalletConfig::get('notifications.retry.backoff_minutes', 5)
            ))
            ->limit($limit)
            ->get();

        $retried = 0;

        foreach ($notifications as $notification) {
            $notification->incrementRetryCount();
            $notification->markAsPending();
            $retried++;
        }

        if ($retried > 0) {
            $results = $this->sendBatch($notifications, $delayBetweenJobs);
        } else {
            $results = ['dispatched' => 0];
        }

        return [
            'retried' => $retried,
            'dispatched' => $results['dispatched'],
        ];
    }

    public function markAsSent(WalletNotification $notification, array $metadata = []): WalletNotification
    {
        return $notification->markAsSent($metadata);
    }

    public function markAsFailed(WalletNotification $notification, string $error, array $metadata = []): WalletNotification
    {
        return $notification->markAsFailed($error, $metadata);
    }

    public function getByStatus(NotificationStatus $status, int $limit = 100): Collection
    {
        $query = WalletNotification::where('notification_status', $status->value);

        if ($status === NotificationStatus::PENDING) {
            $query->oldest();
        } else {
            $query->latest();
        }

        return $query->limit($limit)->get();
    }

    public function getForRecipient(RecipientType $recipientType, int $recipientId, int $limit = 50): Collection
    {
        return WalletNotification::forRecipient($recipientType->value, $recipientId)
            ->latest()
            ->limit($limit)
            ->get();
    }

    public function cleanup(): array
    {
        $results = ['deleted' => 0, 'sent' => 0, 'failed' => 0];

        $sentRetention = WalletConfig::get('notifications.cleanup.sent_retention_days', 30);
        $failedRetention = WalletConfig::get('notifications.cleanup.failed_retention_days', 90);

        if ($sentRetention > 0) {
            $sentCount = WalletNotification::sent()
                ->where('sent_at', '<=', Carbon::now()->subDays($sentRetention))
                ->delete();

            $results['sent'] = $sentCount;
            $results['deleted'] += $sentCount;
        }

        if ($failedRetention > 0) {
            $failedCount = WalletNotification::failed()
                ->where('failed_at', '<=', Carbon::now()->subDays($failedRetention))
                ->delete();

            $results['failed'] = $failedCount;
            $results['deleted'] += $failedCount;
        }

        WalletLog::info()
            ->message('Notification cleanup completed')
            ->context($results)
            ->write();

        return $results;
    }

    public function getStuckNotifications(int $minutesStuck = 30): Collection
    {
        return WalletNotification::pending()
            ->where(function ($query) use ($minutesStuck) {
                // Only immediate notifications (no scheduled_at) that are stuck
                $query->whereNull('scheduled_at')
                    ->where('created_at', '<=', Carbon::now()->subMinutes($minutesStuck));
            })
            ->orWhere(function ($query) use ($minutesStuck) {
                // Or scheduled notifications whose time has passed but still stuck
                $query->whereNotNull('scheduled_at')
                    ->where('scheduled_at', '<=', Carbon::now()->subMinutes($minutesStuck));
            })
            ->get();
    }

    public function recoverStuck(Collection $notifications): array
    {
        $results = ['recovered' => 0, 'failed' => 0];
        $maxRetries = WalletConfig::get('notifications.watchdog.max_recovery_attempts', 2);

        foreach ($notifications as $notification) {
            if ($notification->retry_count >= $maxRetries) {
                $this->markAsFailed($notification, 'Max recovery attempts reached', [
                    'stuck_minutes' => Carbon::now()->diffInMinutes($notification->created_at),
                ]);
                $results['failed']++;
            } else {
                $notification->incrementRetryCount();
                dispatch(new SendWalletNotificationJob($notification));
                $results['recovered']++;
            }
        }

        return $results;
    }

    public function cancel(WalletNotification $notification): bool
    {
        if (!$notification->isScheduled()) {
            throw WalletNotificationException::notificationNotScheduled($notification->id);
        }

        return $notification->update([
            'notification_status' => NotificationStatus::FAILED->value,
            'failed_at' => Carbon::now(),
            'error' => 'Cancelled by user',
        ]);
    }

    public function getStatistics(?\DateTimeInterface $from = null, ?\DateTimeInterface $to = null): array
    {
        $query = WalletNotification::query();

        if ($from) {
            $query->where('created_at', '>=', $from);
        }

        if ($to) {
            $query->where('created_at', '<=', $to);
        }

        $stats = $query->selectRaw('
            notification_status,
            notification_channel,
            notification_type,
            COUNT(*) as count,
            AVG(retry_count) as avg_retries
        ')
        ->groupBy('notification_status', 'notification_channel', 'notification_type')
        ->get();

        $results = [
            'total' => 0,
            'by_status' => [],
            'by_channel' => [],
            'by_type' => [],
            'average_retries' => 0,
        ];

        $totalRetries = 0;
        $totalCount = 0;

        foreach ($stats as $stat) {
            $count = (int) $stat->count;
            $results['total'] += $count;
            $totalCount += $count;
            $totalRetries += $stat->avg_retries * $count;

            $results['by_status'][$stat->notification_status] = ($results['by_status'][$stat->notification_status] ?? 0) + $count;
            $results['by_channel'][$stat->notification_channel] = ($results['by_channel'][$stat->notification_channel] ?? 0) + $count;
            $results['by_type'][$stat->notification_type] = ($results['by_type'][$stat->notification_type] ?? 0) + $count;
        }

        if ($totalCount > 0) {
            $results['average_retries'] = round($totalRetries / $totalCount, 2);
        }

        return $results;
    }

    private function resolveRecipient(WalletNotification $notification): ?object
    {
        $recipientType = strtolower($notification->recipient_type->value);
        $recipientClass = WalletConfig::get("notifications.recipients.{$recipientType}.model");

        if (!$recipientClass || !class_exists($recipientClass)) {
            throw WalletNotificationException::recipientClassNotConfigured($notification->recipient_type->value);
        }

        return $recipientClass::find($notification->recipient_id);
    }

    /**
     * Get recipient's preferred locale
     */
    private function getRecipientLocale(RecipientType $recipientType, int $recipientId): ?string
    {
        $recipientTypeStr = strtolower($recipientType->value);
        $recipientClass = WalletConfig::get("notifications.recipients.{$recipientTypeStr}.model");

        if (!$recipientClass || !class_exists($recipientClass)) {
            return null;
        }

        $recipient = $recipientClass::find($recipientId);
        
        if (!$recipient) {
            return null;
        }

        // Check for locale property
        if (property_exists($recipient, 'locale') && $recipient->locale) {
            return $recipient->locale;
        }

        // Check for language property (some systems use 'language' instead of 'locale')
        if (property_exists($recipient, 'language') && $recipient->language) {
            return $recipient->language;
        }

        // For customers, check if there's a getLocale method
        if (method_exists($recipient, 'getLocale')) {
            return $recipient->getLocale();
        }

        return null;
    }
}