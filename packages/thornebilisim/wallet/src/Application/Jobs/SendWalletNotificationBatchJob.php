<?php

declare(strict_types=1);

namespace Thorne\Wallet\Application\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Collection;
use Thorne\Wallet\Support\Facades\WalletLog;
use Thorne\Wallet\Support\Helpers\WalletConfig;
use Thorne\Wallet\Domain\Models\WalletNotification;

class SendWalletNotificationBatchJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $timeout = 300; // 5 minutes

    public function __construct(
        public Collection $notificationIds,
        public int $delayBetweenJobs = 2
    ) {
        $this->onQueue('wallet-notifications');
    }

    public function handle(): void
    {
        WalletLog::info()
            ->message('Starting batch notification processing')
            ->context([
                'notification_count' => $this->notificationIds->count(),
                'delay_between_jobs' => $this->delayBetweenJobs,
            ])
            ->write();

        foreach ($this->notificationIds as $index => $notificationId) {
            $notification = WalletNotification::find($notificationId);
            
            if (!$notification) {
                WalletLog::warning()
                    ->message('Notification not found during batch processing')
                    ->context(['notification_id' => $notificationId])
                    ->write();
                continue;
            }

            // Calculate delay for this job
            $delay = $index * $this->delayBetweenJobs;
            
            if ($delay > 0) {
                dispatch(new SendWalletNotificationJob($notification))
                    ->delay(now()->addSeconds($delay));
                    
                WalletLog::info()
                    ->message('Dispatched notification job with delay')
                    ->context([
                        'notification_id' => $notification->id,
                        'delay_seconds' => $delay,
                        'scheduled_for' => now()->addSeconds($delay)->toISOString(),
                    ])
                    ->write();
            } else {
                dispatch(new SendWalletNotificationJob($notification));
            }
        }

        WalletLog::info()
            ->message('Batch notification processing completed')
            ->context([
                'processed_count' => $this->notificationIds->count(),
                'total_delay' => ($this->notificationIds->count() - 1) * $this->delayBetweenJobs,
            ])
            ->write();
    }

    public function tags(): array
    {
        return [
            'wallet-notification-batch',
            'batch-size:' . $this->notificationIds->count(),
        ];
    }

    public function displayName(): string
    {
        return sprintf(
            'Send batch of %d wallet notifications (delay: %ds)',
            $this->notificationIds->count(),
            $this->delayBetweenJobs
        );
    }
}