<?php

declare(strict_types=1);

namespace Thorne\Wallet\Application\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\Middleware\RateLimited;
use Illuminate\Queue\SerializesModels;
use Thorne\Wallet\Support\Facades\WalletLog;
use Thorne\Wallet\Support\Helpers\WalletConfig;
use Thorne\Wallet\Support\Traits\HasRetryPolicy;
use Thorne\Wallet\Application\Services\WalletNotificationService;
use Thorne\Wallet\Domain\Models\WalletNotification;
use Thorne\Wallet\Domain\Exceptions\WalletNotificationException;

class SendWalletNotificationJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, HasRetryPolicy;

    /**
     * The number of seconds the job can run before timing out
     *
     * @var int
     */
    public $timeout = 30;

    /**
     * Create a new job instance
     *
     * @param WalletNotification $notification
     */
    public function __construct(
        public WalletNotification $notification
    ) {
        // Set queue based on notification channel
        $this->onQueue($this->getChannelQueue($notification->notification_channel->getConfigKey()));
    }

    /**
     * Get the middleware the job should pass through.
     *
     * @return array
     */
    public function middleware(): array
    {
        $middleware = [];

        // Add rate limiting based on notification channel
        $channelKey = $this->notification->notification_channel->getConfigKey();
        $rateLimitConfig = WalletConfig::get("notifications.channels.{$channelKey}.rate_limit", []);
        
        if ($rateLimitConfig['enabled'] ?? true) {
            $maxAttempts = $rateLimitConfig['max_attempts'] ?? 2;
            $decayMinutes = $rateLimitConfig['decay_minutes'] ?? 1;
            $keyPrefix = $rateLimitConfig['key_prefix'] ?? "wallet_{$channelKey}_rate_limit";
            
            // Create rate limit key - unique per channel
            $middleware[] = new RateLimited("{$keyPrefix}:{$maxAttempts},{$decayMinutes}");
            
            WalletLog::info()
                ->message('Rate limiting applied to notification job')
                ->context([
                    'notification_id' => $this->notification->id,
                    'channel' => $this->notification->notification_channel->value,
                    'rate_limit_key' => $keyPrefix,
                    'max_attempts' => $maxAttempts,
                    'decay_minutes' => $decayMinutes,
                ])
                ->write();
        }

        return $middleware;
    }

    /**
     * Execute the job
     *
     * @param WalletNotificationService $notificationService
     * @return void
     */
    public function handle(WalletNotificationService $notificationService): void
    {
        // Skip if notification is not pending
        if (!$this->notification->isPending()) {
            WalletLog::info()
                ->message('Skipping non-pending notification')
                ->context([
                    'notification_id' => $this->notification->id,
                    'status' => $this->notification->notification_status->value,
                ])
                ->write();
            return;
        }

        // Skip if notifications are disabled
        if (!WalletConfig::get('notifications.enabled', true)) {
            WalletLog::info()
                ->message('Wallet notifications are disabled')
                ->context([
                    'notification_id' => $this->notification->id,
                ])
                ->write();
            return;
        }

        // Check if channel is enabled
        $channelConfig = WalletConfig::get("notifications.channels.{$this->notification->notification_channel->getConfigKey()}", []);
        if (!($channelConfig['enabled'] ?? true)) {
            WalletLog::info()
                ->message("Notification channel {$this->notification->notification_channel->value} is disabled")
                ->context([
                    'notification_id' => $this->notification->id,
                ])
                ->write();
            return;
        }

        // Send the notification
        try {
            $notificationService->send($this->notification);
        } catch (WalletNotificationException $e) {
            // WalletNotificationException already logged and handled in service
            // Just re-throw to trigger job failure
            throw $e;
        }
    }

    /**
     * Get the number of times the job may be attempted.
     *
     * @return int
     */
    public function tries(): int
    {
        return $this->getTries();
    }

    /**
     * Calculate the number of seconds to wait before retrying the job
     *
     * @return array<int, int>
     */
    public function backoff(): array
    {
        return $this->getBackoff();
    }

    /**
     * Handle a job failure
     *
     * @param \Throwable $exception
     * @return void
     */
    public function failed(\Throwable $exception): void
    {
        WalletLog::error()
            ->message('Failed to send wallet notification')
            ->context([
                'notification_id' => $this->notification->id,
                'error' => $exception->getMessage(),
                'trace' => $exception->getTraceAsString(),
            ])
            ->write();

        // Mark notification as failed if max attempts reached
        if ($this->attempts() >= $this->tries()) {
            $this->notification->markAsFailed(
                'Job failed after ' . $this->tries() . ' attempts: ' . $exception->getMessage(),
                [
                    'job_attempts' => $this->attempts(),
                    'exception' => get_class($exception),
                ]
            );
        }
    }

    /**
     * Get the tags that should be assigned to the job
     *
     * @return array<int, string>
     */
    public function tags(): array
    {
        return [
            'wallet-notification',
            'notification-type:' . $this->notification->notification_type->value,
            'notification-channel:' . $this->notification->notification_channel->value,
            'recipient-type:' . $this->notification->recipient_type->value,
        ];
    }

    /**
     * Get the display name for the job
     *
     * @return string
     */
    public function displayName(): string
    {
        return sprintf(
            'Send %s notification via %s to %s #%d',
            $this->notification->notification_type->value,
            $this->notification->notification_channel->value,
            $this->notification->recipient_type->value,
            $this->notification->recipient_id
        );
    }
}