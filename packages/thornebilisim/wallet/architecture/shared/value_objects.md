# Wallet System Value Objects

This document defines all value objects used throughout the wallet system. Value objects provide encapsulation, validation, and behavior for important domain concepts while maintaining immutability.

## Table of Contents

- [Financial Value Objects](#financial-value-objects)
  - [Money](#money)
  - [Currency](#currency)
- [Account Related Value Objects](#account-related-value-objects)
  - [AccountNumber](#accountnumber)
- [Metadata Value Objects](#metadata-value-objects)
  - [Metadata](#metadata)
- [Usage Guidelines](#usage-guidelines)
  - [Creating Value Objects](#creating-value-objects)
  - [Value Object Equality](#value-object-equality)
  - [Serialization](#serialization)
- [Middleware Integration](#middleware-integration)
- [Context Standardization](#context-standardization)
- [Best Practices](#best-practices)

## Financial Value Objects

### Money
Handles monetary amounts with high precision arithmetic and currency validation.

```php
<?php

namespace Thorne\Wallet\Domain\ValueObjects;

use Brick\Money\Money as BrickMoney;
use Brick\Money\Context\CustomContext;
use Thorne\Wallet\Domain\Exceptions\InvalidMoneyException;
use Thorne\Wallet\Support\WalletConfig;

class Money
{
    private BrickMoney $money;
    private const PRECISION = 8;
    private const SCALE = 8;

    private function __construct(BrickMoney $money)
    {
        $this->money = $money;
    }

    public static function of(string|int|float $amount, string $currency): self
    {
        if (!WalletConfig::isCurrencySupported($currency)) {
            throw InvalidMoneyException::unsupportedCurrency($currency);
        }

        $context = new CustomContext(self::PRECISION);
        $money = BrickMoney::of($amount, $currency, $context);

        return new self($money);
    }

    public static function zero(string $currency): self
    {
        return self::of(0, $currency);
    }

    public function getAmount(): string
    {
        return $this->money->getAmount()->toScale(self::SCALE);
    }

    public function getCurrency(): string
    {
        return $this->money->getCurrency()->getCurrencyCode();
    }

    public function add(Money $other): Money
    {
        $this->ensureSameCurrency($other);
        return new self($this->money->plus($other->money));
    }

    public function subtract(Money $other): Money
    {
        $this->ensureSameCurrency($other);
        return new self($this->money->minus($other->money));
    }

    public function multiply(string|int|float $multiplier): Money
    {
        return new self($this->money->multipliedBy($multiplier));
    }

    public function divide(string|int|float $divisor): Money
    {
        return new self($this->money->dividedBy($divisor));
    }

    public function isPositive(): bool
    {
        return $this->money->isPositive();
    }

    public function isNegative(): bool
    {
        return $this->money->isNegative();
    }

    public function isZero(): bool
    {
        return $this->money->isZero();
    }

    public function isGreaterThan(Money $other): bool
    {
        $this->ensureSameCurrency($other);
        return $this->money->isGreaterThan($other->money);
    }

    public function isLessThan(Money $other): bool
    {
        $this->ensureSameCurrency($other);
        return $this->money->isLessThan($other->money);
    }

    public function isEqualTo(Money $other): bool
    {
        $this->ensureSameCurrency($other);
        return $this->money->isEqualTo($other->money);
    }

    public function isGreaterThanOrEqual(Money $other): bool
    {
        return $this->isGreaterThan($other) || $this->isEqualTo($other);
    }

    public function isLessThanOrEqual(Money $other): bool
    {
        return $this->isLessThan($other) || $this->isEqualTo($other);
    }

    public function format(?string $locale = null): string
    {
        $currencyConfig = WalletConfig::getCurrency($this->getCurrency());
        $precision = $currencyConfig['precision'] ?? 2;
        
        return number_format(
            (float) $this->getAmount(),
            $precision,
            '.',
            ','
        ) . ' ' . $currencyConfig['symbol'];
    }

    public function toArray(): array
    {
        return [
            'amount' => $this->getAmount(),
            'currency' => $this->getCurrency(),
        ];
    }

    public function toString(): string
    {
        return $this->getAmount() . ' ' . $this->getCurrency();
    }

    public function __toString(): string
    {
        return $this->toString();
    }

    private function ensureSameCurrency(Money $other): void
    {
        if ($this->getCurrency() !== $other->getCurrency()) {
            throw new InvalidMoneyException(
                "Currency mismatch: {$this->getCurrency()} vs {$other->getCurrency()}"
            );
        }
    }

    public function jsonSerialize(): array
    {
        return $this->toArray();
    }
}
```

**Key Features:**
- **High Precision**: Uses DECIMAL(20,8) precision for accurate calculations
- **Currency Validation**: Validates against supported currencies
- **Immutability**: All operations return new instances
- **Arithmetic Operations**: Add, subtract, multiply, divide with proper validation
- **Comparison Operations**: Complete set of comparison methods
- **Formatting**: Display formatting with currency symbols and precision
- **Serialization**: JSON serialization support

### Currency
Represents supported currencies with validation and configuration access.

```php
<?php

namespace Thorne\Wallet\Domain\ValueObjects;

use Thorne\Wallet\Domain\Exceptions\UnsupportedCurrencyException;
use Thorne\Wallet\Support\WalletConfig;

class Currency
{
    private string $code;
    private array $config;

    private function __construct(string $code, array $config)
    {
        $this->code = strtoupper($code);
        $this->config = $config;
    }

    public static function from(string $code): self
    {
        $code = strtoupper($code);
        
        if (!WalletConfig::isCurrencySupported($code)) {
            throw UnsupportedCurrencyException::notSupported($code);
        }

        $config = WalletConfig::getCurrency($code);
        
        return new self($code, $config);
    }

    public static function getDefault(): self
    {
        $defaultCurrency = WalletConfig::getDefaultCurrency();
        return self::from($defaultCurrency['code']);
    }

    public static function getAllSupported(): array
    {
        return array_map(
            fn($code) => self::from($code),
            WalletConfig::getSupportedCurrencies()
        );
    }

    public function getCode(): string
    {
        return $this->code;
    }

    public function getName(): string
    {
        return $this->config['name'];
    }

    public function getSymbol(): string
    {
        return $this->config['symbol'];
    }

    public function getIsoCode(): int
    {
        return $this->config['iso_code'];
    }

    public function getPrecision(): int
    {
        return $this->config['precision'];
    }

    public function isEnabled(): bool
    {
        return $this->config['enabled'] ?? false;
    }

    public function isDefault(): bool
    {
        return $this->config['is_default'] ?? false;
    }

    public function isCustom(): bool
    {
        return $this->config['is_custom'] ?? false;
    }

    public function isSupported(): bool
    {
        return WalletConfig::isCurrencySupported($this->code);
    }

    public function equals(Currency $other): bool
    {
        return $this->code === $other->code;
    }

    public function toArray(): array
    {
        return [
            'code' => $this->code,
            'name' => $this->getName(),
            'symbol' => $this->getSymbol(),
            'iso_code' => $this->getIsoCode(),
            'precision' => $this->getPrecision(),
            'enabled' => $this->isEnabled(),
            'is_default' => $this->isDefault(),
            'is_custom' => $this->isCustom(),
        ];
    }

    public function toString(): string
    {
        return $this->code;
    }

    public function __toString(): string
    {
        return $this->toString();
    }

    public function jsonSerialize(): array
    {
        return $this->toArray();
    }
}
```

**Key Features:**
- **Configuration Integration**: Reads from `currencies.php` config
- **Validation**: Ensures currency is supported and enabled
- **Default Currency**: Easy access to system default currency
- **Currency Properties**: Access to name, symbol, ISO code, precision
- **Custom Currency Support**: Handles both standard and custom currencies
- **Type Safety**: Immutable value object with validation

## Account Related Value Objects

### AccountNumber
Generates and validates 11-digit account numbers using the Luhn algorithm.

```php
<?php

namespace Thorne\Wallet\Domain\ValueObjects;

use Thorne\Wallet\Domain\Exceptions\InvalidAccountNumberException;
use Thorne\Wallet\Support\WalletConfig;

class AccountNumber
{
    private string $number;
    private const LENGTH = 11;

    private function __construct(string $number)
    {
        $this->validateFormat($number);
        $this->validateChecksum($number);
        $this->number = $number;
    }

    public static function generate(string $currencyCode): self
    {
        $currency = Currency::from($currencyCode);
        $isoCode = str_pad((string) $currency->getIsoCode(), 3, '0', STR_PAD_LEFT);
        
        // Generate 7 random digits
        $randomPart = str_pad((string) random_int(1000000, 9999999), 7, '0', STR_PAD_LEFT);
        
        // Calculate Luhn check digit
        $baseNumber = $isoCode . $randomPart;
        $checkDigit = self::calculateLuhnCheckDigit($baseNumber);
        
        $accountNumber = $baseNumber . $checkDigit;
        
        return new self($accountNumber);
    }

    public static function fromString(string $number): self
    {
        // Remove any formatting
        $number = preg_replace('/[^0-9]/', '', $number);
        
        return new self($number);
    }

    public function getNumber(): string
    {
        return $this->number;
    }

    public function getCurrencyIsoCode(): string
    {
        return substr($this->number, 0, 3);
    }

    public function getSequenceNumber(): string
    {
        return substr($this->number, 3, 7);
    }

    public function getCheckDigit(): string
    {
        return substr($this->number, -1);
    }

    public function getFormatted(): string
    {
        return substr($this->number, 0, 3) . '-' . 
               substr($this->number, 3, 7) . '-' . 
               substr($this->number, -1);
    }

    public function getCurrency(): Currency
    {
        $isoCode = (int) $this->getCurrencyIsoCode();
        
        $supportedCurrencies = WalletConfig::getAllCurrencies();
        foreach ($supportedCurrencies as $code => $config) {
            if ($config['iso_code'] === $isoCode) {
                return Currency::from($code);
            }
        }
        
        throw InvalidAccountNumberException::invalidCurrencyCode((string) $isoCode);
    }

    public function isValid(): bool
    {
        try {
            $this->validateFormat($this->number);
            $this->validateChecksum($this->number);
            return true;
        } catch (InvalidAccountNumberException) {
            return false;
        }
    }

    public function equals(AccountNumber $other): bool
    {
        return $this->number === $other->number;
    }

    public function toArray(): array
    {
        return [
            'number' => $this->number,
            'formatted' => $this->getFormatted(),
            'currency_iso_code' => $this->getCurrencyIsoCode(),
            'sequence_number' => $this->getSequenceNumber(),
            'check_digit' => $this->getCheckDigit(),
        ];
    }

    public function toString(): string
    {
        return $this->number;
    }

    public function __toString(): string
    {
        return $this->toString();
    }

    private function validateFormat(string $number): void
    {
        if (!ctype_digit($number)) {
            throw InvalidAccountNumberException::invalidFormat($number, 'must contain only digits');
        }
        
        if (strlen($number) !== self::LENGTH) {
            throw InvalidAccountNumberException::invalidLength($number, self::LENGTH);
        }
    }

    private function validateChecksum(string $number): void
    {
        $baseNumber = substr($number, 0, -1);
        $providedCheckDigit = (int) substr($number, -1);
        $calculatedCheckDigit = self::calculateLuhnCheckDigit($baseNumber);
        
        if ($providedCheckDigit !== $calculatedCheckDigit) {
            throw InvalidAccountNumberException::invalidChecksum($number);
        }
    }

    private static function calculateLuhnCheckDigit(string $number): int
    {
        $sum = 0;
        $length = strlen($number);
        
        for ($i = 0; $i < $length; $i++) {
            $digit = (int) $number[$length - 1 - $i];
            
            if ($i % 2 === 0) { // Even position (from right)
                $digit *= 2;
                if ($digit > 9) {
                    $digit -= 9;
                }
            }
            
            $sum += $digit;
        }
        
        return (10 - ($sum % 10)) % 10;
    }

    public function jsonSerialize(): array
    {
        return $this->toArray();
    }
}
```

**Key Features:**
- **Luhn Algorithm**: Uses Luhn algorithm for checksum validation
- **Currency Integration**: Embeds currency ISO code in account number
- **Format Validation**: Ensures 11-digit format
- **Generation**: Automatic generation with random sequence numbers
- **Formatting**: Provides formatted display (XXX-XXXXXXX-X)
- **Currency Detection**: Can determine currency from account number

## Metadata Value Objects

### Metadata
Standardized metadata for all domain events providing audit trail and context.

> **Note**: For HTTP requests, metadata is automatically created by the `WalletMetadataMiddleware` and attached to all domain events. Manual creation is typically only needed for system operations, testing, or debugging.

```php
<?php

namespace Thorne\Wallet\Domain\ValueObjects;

use Carbon\Carbon;
use Thorne\Wallet\Domain\Exceptions\InvalidMetadataException;

class Metadata
{
    private ?array $performedBy;
    private ?string $ipAddress;
    private ?string $userAgent;
    private ?string $url;
    private Carbon $occurredAt;
    private array $additionalData;

    public function __construct(
        ?array $performedBy = null,
        ?string $ipAddress = null,
        ?string $userAgent = null,
        ?string $url = null,
        ?Carbon $occurredAt = null,
        array $additionalData = []
    ) {
        $this->performedBy = $this->validatePerformedBy($performedBy);
        $this->ipAddress = $this->validateIpAddress($ipAddress);
        $this->userAgent = $userAgent;
        $this->url = $this->validateUrl($url);
        $this->occurredAt = $occurredAt ?? Carbon::now();
        $this->additionalData = $additionalData;
    }

    /**
     * Create metadata from HTTP request context
     * Note: This is automatically called by WalletMetadataMiddleware
     */
    public static function fromRequest(): self
    {
        $userId = auth()->id();
        return new self(
            performedBy: $userId ? ['type' => 'user', 'id' => (string) $userId] : null,
            ipAddress: request()->ip(),
            userAgent: request()->userAgent(),
            url: request()->fullUrl(),
            occurredAt: Carbon::now(),
            additionalData: ['source' => 'http']
        );
    }

    /**
     * Create metadata for system operations (commands, jobs, observers, sagas)
     * 
     * @param string $context The system context (COMMAND, JOB, OBSERVER, SAGA, SCHEDULER)
     * @param array $additionalData Context-specific data
     */
    public static function fromSystem(string $context, array $additionalData = []): self
    {
        return new self(
            performedBy: ['type' => 'system', 'id' => strtoupper($context)],
            occurredAt: Carbon::now(),
            additionalData: array_merge(['source' => 'system', 'context' => $context], $additionalData)
        );
    }

    /**
     * Create metadata manually (for testing or debugging)
     */
    public static function manual(array $data = []): self
    {
        return new self(
            performedBy: $data['performed_by'] ?? ['type' => 'manual', 'id' => 'MANUAL'],
            ipAddress: $data['ip_address'] ?? null,
            userAgent: $data['user_agent'] ?? null,
            url: $data['url'] ?? null,
            occurredAt: isset($data['occurred_at']) ? Carbon::parse($data['occurred_at']) : Carbon::now(),
            additionalData: array_merge(['source' => 'manual'], $data['additional_data'] ?? [])
        );
    }

    public function getPerformedBy(): ?array
    {
        return $this->performedBy;
    }

    public function getIpAddress(): ?string
    {
        return $this->ipAddress;
    }

    public function getUserAgent(): ?string
    {
        return $this->userAgent;
    }

    public function getUrl(): ?string
    {
        return $this->url;
    }

    public function getOccurredAt(): Carbon
    {
        return $this->occurredAt;
    }

    public function getAdditionalData(): array
    {
        return $this->additionalData;
    }

    public function withAdditionalData(array $data): self
    {
        return new self(
            $this->performedBy,
            $this->ipAddress,
            $this->userAgent,
            $this->url,
            $this->occurredAt,
            array_merge($this->additionalData, $data)
        );
    }

    public function toArray(): array
    {
        return [
            'performed_by' => $this->performedBy,
            'ip_address' => $this->ipAddress,
            'user_agent' => $this->userAgent,
            'url' => $this->url,
            'occurred_at' => $this->occurredAt->toISOString(),
            'additional_data' => $this->additionalData,
        ];
    }

    public function jsonSerialize(): array
    {
        return $this->toArray();
    }

    private function validateIpAddress(?string $ipAddress): ?string
    {
        if ($ipAddress === null) {
            return null;
        }

        if (!filter_var($ipAddress, FILTER_VALIDATE_IP)) {
            throw InvalidMetadataException::invalidIpAddress($ipAddress);
        }

        return $ipAddress;
    }

    private function validateUrl(?string $url): ?string
    {
        if ($url === null) {
            return null;
        }

        if (!filter_var($url, FILTER_VALIDATE_URL)) {
            throw InvalidMetadataException::invalidUrl($url);
        }

        return $url;
    }

    private function validatePerformedBy(?array $performedBy): ?array
    {
        if ($performedBy === null) {
            return null;
        }

        if (!isset($performedBy['type']) || !isset($performedBy['id'])) {
            throw InvalidMetadataException::invalidPerformedBy(
                "performedBy must have 'type' and 'id' keys",
                $performedBy
            );
        }

        if (!is_string($performedBy['type']) || !is_string($performedBy['id'])) {
            throw InvalidMetadataException::invalidPerformedBy(
                "performedBy 'type' and 'id' must be strings",
                $performedBy
            );
        }

        $validTypes = ['user', 'system', 'manual'];
        if (!in_array($performedBy['type'], $validTypes)) {
            throw InvalidMetadataException::invalidPerformedBy(
                "performedBy 'type' must be one of: " . implode(', ', $validTypes),
                $performedBy
            );
        }

        return $performedBy;
    }
}
```

**Key Features:**
- **Request Context**: Automatically captures HTTP request context
- **User Tracking**: Tracks who performed the action
- **Security Information**: IP address and user agent for audit
- **Timestamps**: Precise occurrence time with Carbon
- **Extensibility**: Additional data support for custom context
- **Validation**: URL and IP address validation
- **Factory Methods**: Convenient creation from request or system context

## Usage Guidelines

### Creating Value Objects

#### Money and Currency
```php
// Money
$amount = Money::of('100.50', 'EUR');
$zero = Money::zero('USD');

// Currency
$euro = Currency::from('EUR');
$default = Currency::getDefault();

// AccountNumber
$accountNumber = AccountNumber::generate('EUR');
$existing = AccountNumber::fromString('978-1234567-8');
```

#### Metadata Creation Patterns

**HTTP Requests (Automatic via Middleware):**
```php
// This happens automatically in WalletMetadataMiddleware
$metadata = Metadata::fromRequest();
// Contains: user ID, IP, user agent, URL, timestamp
```

**System Operations:**
```php
// Command execution
$metadata = Metadata::fromSystem('COMMAND', [
    'command' => 'wallet:reconcile-balances',
    'arguments' => ['--batch-size=100', '--currency=EUR'],
    'schedule' => 'daily'
]);

// Queue Job processing
$metadata = Metadata::fromSystem('JOB', [
    'job' => 'ProcessWalletTransaction',
    'queue' => 'payments',
    'job_id' => $jobId,
    'attempt' => 1
]);

// Model Observer events
$metadata = Metadata::fromSystem('OBSERVER', [
    'observer' => 'WalletAccountObserver',
    'event' => 'created',
    'model' => 'WalletAccount',
    'model_id' => $accountId
]);

// Saga step execution
$metadata = Metadata::fromSystem('SAGA', [
    'saga' => 'TransactionProcessingSaga',
    'saga_id' => $sagaId,
    'step' => 'ValidateBalance',
    'correlation_id' => $correlationId
]);

// Scheduled task execution
$metadata = Metadata::fromSystem('SCHEDULER', [
    'task' => 'daily-balance-report',
    'schedule' => '0 2 * * *',
    'execution_id' => uniqid()
]);
```

**Manual Creation (Testing/Debugging):**
```php
// Basic manual metadata
$metadata = Metadata::manual([
    'performed_by' => 'TEST_USER',
    'additional_data' => ['test_scenario' => 'balance_overflow']
]);

// Complex manual metadata with full context
$metadata = Metadata::manual([
    'performed_by' => 'INTEGRATION_TEST',
    'ip_address' => '127.0.0.1',
    'user_agent' => 'PHPUnit/Test',
    'url' => 'http://localhost/test',
    'occurred_at' => '2024-01-15 10:30:00',
    'additional_data' => [
        'test_case' => 'WalletBalanceTest::testLargeDeposit',
        'environment' => 'testing'
    ]
]);
```

### Value Object Equality
```php
$money1 = Money::of('100', 'EUR');
$money2 = Money::of('100.00', 'EUR');
$isEqual = $money1->isEqualTo($money2); // true

$currency1 = Currency::from('EUR');
$currency2 = Currency::from('EUR');
$isEqual = $currency1->equals($currency2); // true
```

### Serialization
```php
$money = Money::of('100.50', 'EUR');
$json = json_encode($money); // {"amount":"100.50000000","currency":"EUR"}

$array = $money->toArray(); // ['amount' => '100.50000000', 'currency' => 'EUR']
```

## Middleware Integration

### WalletMetadataMiddleware
The `WalletMetadataMiddleware` automatically creates and injects metadata for all HTTP requests that trigger wallet operations:

```php
// middleware/WalletMetadataMiddleware.php
class WalletMetadataMiddleware
{
    public function handle($request, Closure $next)
    {
        // Auto-inject metadata for wallet operations
        app()->singleton(Metadata::class, function () {
            return Metadata::fromRequest();
        });
        
        return $next($request);
    }
}
```

**When middleware is active:**
- All domain events automatically receive HTTP context metadata
- No manual metadata creation needed in controllers or actions
- Consistent audit trail across all user-initiated operations

**When middleware is not active:**
- System operations require manual metadata creation
- Use appropriate `fromSystem()` context for each operation type
- Testing scenarios use `manual()` factory method

## Context Standardization

### System Context Types
Use these standardized context types for `fromSystem()`:

- **`COMMAND`**: Artisan commands, CLI operations
- **`JOB`**: Queue jobs, background processing
- **`OBSERVER`**: Model observers, Eloquent events
- **`SAGA`**: Saga step execution
- **`SCHEDULER`**: Scheduled tasks, cron jobs

### Additional Data Structure
Recommended structure for `additionalData`:

```php
[
    'source' => 'system',           // Auto-added
    'context' => 'COMMAND',         // Auto-added
    'operation' => 'specific_name', // Operation identifier
    'batch_id' => 'uuid',          // Batch/group identifier
    'correlation_id' => 'uuid',     // Cross-system correlation
    'environment' => 'production',  // Environment context
    'version' => '1.0.0'           // Application version
]
```

## Best Practices

1. **Immutability**: Value objects are immutable - all operations return new instances
2. **Validation**: Always validate input in constructors
3. **Factory Methods**: Provide convenient factory methods for common creation patterns
4. **Equality**: Implement proper equality methods based on value equality
5. **Serialization**: Support JSON serialization for API responses
6. **Type Safety**: Use strict typing and validation
7. **Business Logic**: Encapsulate business rules within value objects
8. **Documentation**: Document precision, constraints, and business rules
9. **Metadata Consistency**: Use standardized context types and additional data structures
10. **Middleware Integration**: Leverage automatic metadata injection for HTTP operations