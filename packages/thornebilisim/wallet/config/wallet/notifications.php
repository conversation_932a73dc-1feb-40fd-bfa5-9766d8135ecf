<?php

use Webkul\Customer\Models\Customer;
use Webkul\User\Models\Admin;

return [
    /*
    |--------------------------------------------------------------------------
    | Wallet Notification System Configuration
    |--------------------------------------------------------------------------
    |
    | This configuration manages the wallet notification system including
    | channels, recipients, retry policies, and notification routing.
    |
    | The system uses a simplified registry structure:
    | NOTIFICATION_TYPE -> RECIPIENT_TYPE -> NotificationClass
    |
    */

    /*
    |--------------------------------------------------------------------------
    | Global Notification System Settings
    |--------------------------------------------------------------------------
    | Global enable/disable switch for the entire notification system.
    | When disabled, no notifications will be sent.
    |
    | IMPORTANT: Config should not contain runtime code that requires
    | database connections when config:cache is compiled.
    |
    */
    'enabled' => env('WALLET_NOTIFICATIONS_ENABLED', true),

    /*
    |--------------------------------------------------------------------------
    | Notification Channels Configuration
    |--------------------------------------------------------------------------
    | Configuration for each notification channel including enable/disable
    | switches, queue settings, and throttling policies.
    |
    */
    'channels' => [
        'email' => [
            'enabled' => env('WALLET_NOTIFICATIONS_EMAIL_ENABLED', true),
            'from' => [
                'address' => env('WALLET_NOTIFICATIONS_EMAIL_FROM_ADDRESS', '<EMAIL>'),
                'name' => env('WALLET_NOTIFICATIONS_EMAIL_FROM_NAME', 'Wallet System'),
            ],
            'queue' => env('WALLET_NOTIFICATIONS_EMAIL_QUEUE', 'wallet-notifications'),
            'throttle' => [
                'limit' => env('WALLET_NOTIFICATIONS_EMAIL_THROTTLE_LIMIT', 3),
                'duration' => env('WALLET_NOTIFICATIONS_EMAIL_THROTTLE_DURATION', 60),
            ],
            'rate_limit' => [
                'enabled' => env('WALLET_NOTIFICATIONS_EMAIL_RATE_LIMIT_ENABLED', true),
                'max_attempts' => env('WALLET_NOTIFICATIONS_EMAIL_RATE_LIMIT_MAX_ATTEMPTS', 5), // Max emails per duration
                'decay_minutes' => env('WALLET_NOTIFICATIONS_EMAIL_RATE_LIMIT_DECAY_MINUTES', 10), // Rate limit window in minutes
                'key_prefix' => 'wallet_email_rate_limit',
            ],
        ],

        'sms' => [
            'enabled' => env('WALLET_NOTIFICATIONS_SMS_ENABLED', false), // TODO: Implement SMS provider integration (laravel-notification-channels/twilio or nexmo)
            'queue' => env('WALLET_NOTIFICATIONS_SMS_QUEUE', 'wallet-notifications'),
            'provider' => env('WALLET_NOTIFICATIONS_SMS_PROVIDER', 'twilio'),
            'throttle' => [
                'limit' => env('WALLET_NOTIFICATIONS_SMS_THROTTLE_LIMIT', 3),
                'duration' => env('WALLET_NOTIFICATIONS_SMS_THROTTLE_DURATION', 60),
            ],
            'rate_limit' => [
                'enabled' => env('WALLET_NOTIFICATIONS_SMS_RATE_LIMIT_ENABLED', true),
                'max_attempts' => env('WALLET_NOTIFICATIONS_SMS_RATE_LIMIT_MAX_ATTEMPTS', 5), // Max SMS per duration
                'decay_minutes' => env('WALLET_NOTIFICATIONS_SMS_RATE_LIMIT_DECAY_MINUTES', 1), // Rate limit window in minutes
                'key_prefix' => 'wallet_sms_rate_limit',
            ],
        ],

        'push' => [
            'enabled' => env('WALLET_NOTIFICATIONS_PUSH_ENABLED', false), // TODO: Implement push notification integration (laravel-notification-channels/onesignal)
            'queue' => env('WALLET_NOTIFICATIONS_PUSH_QUEUE', 'wallet-notifications'),
            'provider' => env('WALLET_NOTIFICATIONS_PUSH_PROVIDER', 'onesignal'),
            'throttle' => [
                'limit' => env('WALLET_NOTIFICATIONS_PUSH_THROTTLE_LIMIT', 3),
                'duration' => env('WALLET_NOTIFICATIONS_PUSH_THROTTLE_DURATION', 60),
            ],
            'rate_limit' => [
                'enabled' => env('WALLET_NOTIFICATIONS_PUSH_RATE_LIMIT_ENABLED', true),
                'max_attempts' => env('WALLET_NOTIFICATIONS_PUSH_RATE_LIMIT_MAX_ATTEMPTS', 10), // Max push notifications per duration
                'decay_minutes' => env('WALLET_NOTIFICATIONS_PUSH_RATE_LIMIT_DECAY_MINUTES', 1), // Rate limit window in minutes
                'key_prefix' => 'wallet_push_rate_limit',
            ],
        ],

        'alert' => [
            'enabled' => env('WALLET_NOTIFICATIONS_ALERT_ENABLED', false), // TODO: Implement Slack/Teams integration (laravel-notification-channels/microsoft-teams or slack)
            'driver' => env('WALLET_NOTIFICATIONS_ALERT_DRIVER', 'teams'),
            'queue' => env('WALLET_NOTIFICATIONS_ALERT_QUEUE', 'wallet-notifications'),
            'critical_types' => [
                'WALLET_BALANCE_MISMATCH',
                'WALLET_SECURITY_BREACH',
                'WALLET_FRAUD_DETECTED',
                'RECONCILIATION_FAILED',
                'SYSTEM_ERROR',
            ],
            'throttle' => [
                'limit' => env('WALLET_NOTIFICATIONS_ALERT_THROTTLE_LIMIT', 5),
                'duration' => env('WALLET_NOTIFICATIONS_ALERT_THROTTLE_DURATION', 300),
            ],
            'rate_limit' => [
                'enabled' => env('WALLET_NOTIFICATIONS_ALERT_RATE_LIMIT_ENABLED', true),
                'max_attempts' => env('WALLET_NOTIFICATIONS_ALERT_RATE_LIMIT_MAX_ATTEMPTS', 3), // Max alerts per duration
                'decay_minutes' => env('WALLET_NOTIFICATIONS_ALERT_RATE_LIMIT_DECAY_MINUTES', 5), // Rate limit window in minutes
                'key_prefix' => 'wallet_alert_rate_limit',
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Recipient Groups Configuration
    |--------------------------------------------------------------------------
    | Configuration for different types of notification recipients
    | and their associated channels.
    |
    */
    'recipients' => [
        'customer' => [
            'model' => Customer::class,
            'channels' => ['email', 'sms', 'push'],
        ],
        'admin' => [
            'model' => Admin::class,
            'channels' => ['email', 'alert'],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Retry Configuration
    |--------------------------------------------------------------------------
    | Retry policy settings for failed notifications including
    | maximum attempts and backoff strategies.
    |
    */
    'retry' => [
        'max_attempts' => env('WALLET_NOTIFICATIONS_RETRY_MAX_ATTEMPTS', 3),
        'backoff_strategy' => env('WALLET_NOTIFICATIONS_RETRY_STRATEGY', 'exponential'), // linear, exponential, fixed, custom
        'backoff_minutes' => env('WALLET_NOTIFICATIONS_RETRY_BACKOFF_MINUTES', 5),
        'backoff_delays' => [60, 240, 960], // seconds - used only with 'custom' strategy
    ],

    /*
    |--------------------------------------------------------------------------
    | Admin Role-Based Notification Targeting
    |--------------------------------------------------------------------------
    | Simple role-based notification targeting. Each role contains admin IDs
    | that should receive notifications for that domain area.
    | All roles are managed via ENV variables for easy deployment configuration.
    |
    */
    'admin_roles' => [
        // Super admin role - receives all critical notifications
        'super' => [
            'admin_ids' => explode(',', env('WALLET_NOTIFICATIONS_SUPER_ADMIN_IDS', '1')),
        ],

        // Finance role - balance, transaction, and financial notifications
        'finance' => [
            'admin_ids' => explode(',', env('WALLET_NOTIFICATIONS_FINANCE_ADMIN_IDS', '10,11,12')),
        ],

        // Security role - restriction, fraud, and security notifications
        'security' => [
            'admin_ids' => explode(',', env('WALLET_NOTIFICATIONS_SECURITY_ADMIN_IDS', '20,21')),
        ],

        // System role - technical and infrastructure notifications
        'system' => [
            'admin_ids' => explode(',', env('WALLET_NOTIFICATIONS_SYSTEM_ADMIN_IDS', '30,31')),
        ],

        // Reconciliation role - reconciliation and Web3 integration notifications
        'reconciliation' => [
            'admin_ids' => explode(',', env('WALLET_NOTIFICATIONS_RECONCILIATION_ADMIN_IDS', '40,41,42')),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Cleanup Configuration
    |--------------------------------------------------------------------------
    | Database cleanup settings for notification retention.
    |
    */
    'cleanup' => [
        'sent_retention_days' => env('WALLET_NOTIFICATIONS_SENT_RETENTION', 30),
        'failed_retention_days' => env('WALLET_NOTIFICATIONS_FAILED_RETENTION', 90),
    ],

    /*
    |--------------------------------------------------------------------------
    | Watchdog Configuration
    |--------------------------------------------------------------------------
    | Settings for the stuck notification recovery system.
    |
    */
    'watchdog' => [
        'enabled' => env('WALLET_NOTIFICATIONS_WATCHDOG_ENABLED', true),
        'timeout_minutes' => env('WALLET_NOTIFICATIONS_WATCHDOG_TIMEOUT', 30), // Minutes before notification considered stuck
        'max_recovery_attempts' => env('WALLET_NOTIFICATIONS_WATCHDOG_MAX_RECOVERY', 2), // Max retry attempts for stuck notifications
    ],

    /*
    |--------------------------------------------------------------------------
    | Notification Class Registry (Simplified Registry)
    |--------------------------------------------------------------------------
    | NOTIFICATION_TYPE -> RECIPIENT_TYPE -> NotificationClass
    |
    | Each NotificationClass determines which channels to use in its via() method.
    | This structure provides a less repetitive and more flexible configuration.
    |
    */
    'registry' => [
        //
    ],
];
