<?php

declare(strict_types=1);

namespace Thorne\Wallet\Tests\Unit\Domain\ValueObjects;

use Brick\Money\Money as BrickMoney;
use Thorne\Wallet\Domain\Exceptions\InvalidMoneyException;
use Thorne\Wallet\Domain\ValueObjects\Money;

/**
 * @covers \Thorne\Wallet\Domain\ValueObjects\Money
 */
final class MoneyTest extends ValueObjectTestCase
{
    /**
     * @test
     */
    public function it_should_create_money_with_valid_currency(): void
    {
        // Arrange & Act
        $money = Money::of('100.50', 'EUR');

        // Assert
        $this->assertInstanceOf(Money::class, $money);
        $this->assertEquals('100.50000000', $money->getAmount());
        $this->assertEquals('EUR', $money->getCurrency());
    }

    /**
     * @test
     */
    public function it_should_create_money_with_integer_amount(): void
    {
        // Arrange & Act
        $money = Money::of(100, 'EUR');

        // Assert
        $this->assertEquals('100.00000000', $money->getAmount());
        $this->assertEquals('EUR', $money->getCurrency());
    }

    /**
     * @test
     */
    public function it_should_create_money_with_float_amount(): void
    {
        // Arrange & Act
        $money = Money::of(100.75, 'EUR');

        // Assert
        $this->assertEquals('100.75000000', $money->getAmount());
        $this->assertEquals('EUR', $money->getCurrency());
    }

    /**
     * @test
     */
    public function it_should_throw_exception_for_unsupported_currency(): void
    {
        // Arrange
        $this->expectException(InvalidMoneyException::class);
        $this->expectExceptionMessage('Unsupported currency: INVALID');

        // Act
        Money::of('100.00', 'INVALID');
    }

    /**
     * @test
     */
    public function it_should_create_zero_money(): void
    {
        // Arrange & Act
        $money = Money::zero('EUR');

        // Assert
        $this->assertEquals('0.00000000', $money->getAmount());
        $this->assertEquals('EUR', $money->getCurrency());
        $this->assertTrue($money->isZero());
    }

    /**
     * @test
     */
    public function it_should_add_two_money_amounts(): void
    {
        // Arrange
        $money1 = Money::of('100.50', 'EUR');
        $money2 = Money::of('50.25', 'EUR');

        // Act
        $result = $money1->add($money2);

        // Assert
        $this->assertInstanceOf(Money::class, $result);
        $this->assertEquals('150.75000000', $result->getAmount());
        $this->assertEquals('EUR', $result->getCurrency());
    }

    /**
     * @test
     */
    public function it_should_subtract_two_money_amounts(): void
    {
        // Arrange
        $money1 = Money::of('100.50', 'EUR');
        $money2 = Money::of('50.25', 'EUR');

        // Act
        $result = $money1->subtract($money2);

        // Assert
        $this->assertInstanceOf(Money::class, $result);
        $this->assertEquals('50.25000000', $result->getAmount());
        $this->assertEquals('EUR', $result->getCurrency());
    }

    /**
     * @test
     */
    public function it_should_multiply_money_by_factor(): void
    {
        // Arrange
        $money = Money::of('100.00', 'EUR');

        // Act
        $result = $money->multiply(2.5);

        // Assert
        $this->assertEquals('250.00000000', $result->getAmount());
        $this->assertEquals('EUR', $result->getCurrency());
    }

    /**
     * @test
     */
    public function it_should_multiply_money_by_string_factor(): void
    {
        // Arrange
        $money = Money::of('100.00', 'EUR');

        // Act
        $result = $money->multiply('1.5');

        // Assert
        $this->assertEquals('150.00000000', $result->getAmount());
        $this->assertEquals('EUR', $result->getCurrency());
    }

    /**
     * @test
     */
    public function it_should_divide_money_by_divisor(): void
    {
        // Arrange
        $money = Money::of('100.00', 'EUR');

        // Act
        $result = $money->divide(4);

        // Assert
        $this->assertEquals('25.00000000', $result->getAmount());
        $this->assertEquals('EUR', $result->getCurrency());
    }

    /**
     * @test
     */
    public function it_should_divide_money_by_string_divisor(): void
    {
        // Arrange
        $money = Money::of('100.00', 'EUR');

        // Act
        $result = $money->divide('2.5');

        // Assert
        $this->assertEquals('40.00000000', $result->getAmount());
        $this->assertEquals('EUR', $result->getCurrency());
    }

    /**
     * @test
     */
    public function it_should_check_if_money_is_positive(): void
    {
        // Arrange
        $positiveMoney = Money::of('100.00', 'EUR');
        $negativeMoney = Money::of('-50.00', 'EUR');
        $zeroMoney = Money::zero('EUR');

        // Act & Assert
        $this->assertTrue($positiveMoney->isPositive());
        $this->assertFalse($negativeMoney->isPositive());
        $this->assertFalse($zeroMoney->isPositive());
    }

    /**
     * @test
     */
    public function it_should_check_if_money_is_negative(): void
    {
        // Arrange
        $positiveMoney = Money::of('100.00', 'EUR');
        $negativeMoney = Money::of('-50.00', 'EUR');
        $zeroMoney = Money::zero('EUR');

        // Act & Assert
        $this->assertFalse($positiveMoney->isNegative());
        $this->assertTrue($negativeMoney->isNegative());
        $this->assertFalse($zeroMoney->isNegative());
    }

    /**
     * @test
     */
    public function it_should_check_if_money_is_zero(): void
    {
        // Arrange
        $positiveMoney = Money::of('100.00', 'EUR');
        $zeroMoney = Money::zero('EUR');
        $zeroFromString = Money::of('0.00', 'EUR');

        // Act & Assert
        $this->assertFalse($positiveMoney->isZero());
        $this->assertTrue($zeroMoney->isZero());
        $this->assertTrue($zeroFromString->isZero());
    }

    /**
     * @test
     */
    public function it_should_compare_money_amounts_greater_than(): void
    {
        // Arrange
        $money1 = Money::of('100.00', 'EUR');
        $money2 = Money::of('50.00', 'EUR');
        $money3 = Money::of('100.00', 'EUR');

        // Act & Assert
        $this->assertTrue($money1->isGreaterThan($money2));
        $this->assertFalse($money2->isGreaterThan($money1));
        $this->assertFalse($money1->isGreaterThan($money3));
    }

    /**
     * @test
     */
    public function it_should_compare_money_amounts_less_than(): void
    {
        // Arrange
        $money1 = Money::of('50.00', 'EUR');
        $money2 = Money::of('100.00', 'EUR');
        $money3 = Money::of('50.00', 'EUR');

        // Act & Assert
        $this->assertTrue($money1->isLessThan($money2));
        $this->assertFalse($money2->isLessThan($money1));
        $this->assertFalse($money1->isLessThan($money3));
    }

    /**
     * @test
     */
    public function it_should_check_money_equality(): void
    {
        // Arrange
        $money1 = Money::of('100.00', 'EUR');
        $money2 = Money::of('100.00', 'EUR');
        $money3 = Money::of('50.00', 'EUR');

        // Act & Assert
        $this->assertTrue($money1->equals($money2));
        $this->assertFalse($money1->equals($money3));
    }

    /**
     * @test
     */
    public function it_should_expose_underlying_brick_money(): void
    {
        // Arrange
        $money = Money::of('100.50', 'EUR');

        // Act
        $brickMoney = $money->toBrickMoney();

        // Assert
        $this->assertInstanceOf(BrickMoney::class, $brickMoney);
        $this->assertEquals('100.50000000', $brickMoney->getAmount()->toScale(8));
        $this->assertEquals('EUR', $brickMoney->getCurrency()->getCurrencyCode());
    }

    /**
     * @test
     */
    public function it_should_maintain_immutability_during_operations(): void
    {
        // Arrange
        $originalMoney = Money::of('100.00', 'EUR');
        $addend = Money::of('50.00', 'EUR');

        // Act
        $resultMoney = $originalMoney->add($addend);

        // Assert
        $this->assertNotSame($originalMoney, $resultMoney);
        $this->assertEquals('100.00000000', $originalMoney->getAmount());
        $this->assertEquals('150.00000000', $resultMoney->getAmount());
    }

    /**
     * @test
     */
    public function it_should_handle_high_precision_calculations(): void
    {
        // Arrange
        $money1 = Money::of('0.12345678', 'EUR');
        $money2 = Money::of('0.87654321', 'EUR');

        // Act
        $result = $money1->add($money2);

        // Assert
        $this->assertEquals('0.99999999', $result->getAmount());
        $this->assertEquals('EUR', $result->getCurrency());
    }

    /**
     * @test
     */
    public function it_should_convert_to_array(): void
    {
        // Arrange
        $money = Money::of('100.50', 'EUR');

        // Act
        $array = $money->toArray();

        // Assert
        $this->assertIsArray($array);
        $this->assertEquals('100.50000000', $array['amount']);
        $this->assertEquals('EUR', $array['currency']);
        $this->assertCount(2, $array);
    }

    /**
     * @test
     */
    public function it_should_convert_to_string(): void
    {
        // Arrange
        $money = Money::of('100.50', 'EUR');

        // Act & Assert
        $this->assertEquals('100.50000000 EUR', $money->toString());
        $this->assertEquals('100.50000000 EUR', (string) $money);
    }

    /**
     * @test
     */
    public function it_should_be_json_serializable(): void
    {
        // Arrange
        $money = Money::of('100.50', 'EUR');

        // Act
        $json = json_encode($money);
        $decoded = json_decode($json, true);

        // Assert
        $this->assertJson($json);
        $this->assertEquals($money->toArray(), $decoded);
        $this->assertEquals('100.50000000', $decoded['amount']);
        $this->assertEquals('EUR', $decoded['currency']);
    }

    /**
     * @test
     */
    public function it_should_handle_currency_mismatch_in_brick_money_operations(): void
    {
        // Arrange
        $eurMoney = Money::of('100.00', 'EUR');
        $usdMoney = Money::of('100.00', 'USD');

        // Act & Assert - Brick/Money should handle currency validation
        $this->expectException(\Brick\Money\Exception\CurrencyMismatchException::class);
        $eurMoney->add($usdMoney);
    }

    /**
     * @test
     * @dataProvider arithmeticOperationsProvider
     */
    public function it_should_handle_various_arithmetic_operations(
        string $amount1,
        string $amount2,
        string $operation,
        string $expected
    ): void {
        // Arrange
        $money1 = Money::of($amount1, 'EUR');
        $money2 = Money::of($amount2, 'EUR');

        // Act
        $result = match ($operation) {
            'add' => $money1->add($money2),
            'subtract' => $money1->subtract($money2),
            default => throw new \InvalidArgumentException("Unknown operation: {$operation}")
        };

        // Assert
        $this->assertEquals($expected, $result->getAmount());
    }

    /**
     * Data provider for arithmetic operations testing.
     */
    public static function arithmeticOperationsProvider(): array
    {
        return [
            'simple_addition' => ['100.00', '50.00', 'add', '150.00000000'],
            'simple_subtraction' => ['100.00', '30.00', 'subtract', '70.00000000'],
            'decimal_addition' => ['10.25', '5.75', 'add', '16.00000000'],
            'decimal_subtraction' => ['10.75', '5.25', 'subtract', '5.50000000'],
            'precision_addition' => ['0.12345678', '0.87654321', 'add', '0.99999999'],
            'precision_subtraction' => ['1.00000000', '0.12345678', 'subtract', '0.87654322'],
        ];
    }

    /**
     * @test
     * @dataProvider multiplicationProvider
     */
    public function it_should_handle_various_multiplication_operations(
        string $amount,
        string|int|float $multiplier,
        string $expected
    ): void {
        // Arrange
        $money = Money::of($amount, 'EUR');

        // Act
        $result = $money->multiply($multiplier);

        // Assert
        $this->assertEquals($expected, $result->getAmount());
    }

    /**
     * Data provider for multiplication operations testing.
     */
    public static function multiplicationProvider(): array
    {
        return [
            'multiply_by_integer' => ['100.00', 2, '200.00000000'],
            'multiply_by_float' => ['100.00', 1.5, '150.00000000'],
            'multiply_by_string' => ['100.00', '2.5', '250.00000000'],
            'multiply_by_decimal' => ['123.45', 0.1, '12.34500000'],
            'multiply_precision' => ['0.12345678', 2, '0.24691356'],
        ];
    }

    /**
     * @test
     * @dataProvider divisionProvider
     */
    public function it_should_handle_various_division_operations(
        string $amount,
        string|int|float $divisor,
        string $expected
    ): void {
        // Arrange
        $money = Money::of($amount, 'EUR');

        // Act
        $result = $money->divide($divisor);

        // Assert
        $this->assertEquals($expected, $result->getAmount());
    }

    /**
     * Data provider for division operations testing.
     */
    public static function divisionProvider(): array
    {
        return [
            'divide_by_integer' => ['100.00', 2, '50.00000000'],
            'divide_by_float' => ['100.00', 2.5, '40.00000000'],
            'divide_by_string' => ['100.00', '4', '25.00000000'],
            'divide_precision' => ['1.00000000', 3, '0.33333333'],
        ];
    }

    /**
     * @test
     */
    public function it_should_handle_edge_case_amounts(): void
    {
        // Arrange & Act
        $verySmall = Money::of('0.00000001', 'EUR');
        $veryLarge = Money::of('999999999999.99999999', 'EUR');
        $zero = Money::zero('EUR');

        // Assert
        $this->assertEquals('0.00000001', $verySmall->getAmount());
        $this->assertEquals('999999999999.99999999', $veryLarge->getAmount());
        $this->assertEquals('0.00000000', $zero->getAmount());
        
        $this->assertTrue($verySmall->isPositive());
        $this->assertTrue($veryLarge->isPositive());
        $this->assertTrue($zero->isZero());
    }

    /**
     * @test
     */
    public function it_should_maintain_precision_in_complex_calculations(): void
    {
        // Arrange
        $money = Money::of('100.12345678', 'EUR');

        // Act - Chain operations
        $result = $money
            ->multiply('1.5')
            ->divide(2)
            ->add(Money::of('0.00000001', 'EUR'));

        // Assert
        $this->assertEquals('75.09259018', $result->getAmount());
        $this->assertEquals('EUR', $result->getCurrency());
    }
}