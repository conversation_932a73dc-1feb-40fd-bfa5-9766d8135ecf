<?php

declare(strict_types=1);

namespace Thorne\Wallet\Tests\Unit\Domain\ValueObjects;

use Carbon\Carbon;
use Thorne\Wallet\Domain\Exceptions\InvalidMetadataException;
use Thorne\Wallet\Domain\ValueObjects\Metadata;

/**
 * @covers \Thorne\Wallet\Domain\ValueObjects\Metadata
 */
final class MetadataTest extends ValueObjectTestCase
{
    protected function setUp(): void
    {
        parent::setUp();
        
        // Mock Carbon for consistent testing
        Carbon::setTestNow('2024-01-15 10:30:00');
    }

    protected function tearDown(): void
    {
        Carbon::setTestNow();
        parent::tearDown();
    }

    /**
     * @test
     */
    public function it_should_create_metadata_with_all_parameters(): void
    {
        // Arrange
        $performedBy = ['type' => 'user', 'id' => '123'];
        $ipAddress = '***********';
        $userAgent = 'Mozilla/5.0';
        $url = 'https://example.com/test';
        $occurredAt = Carbon::parse('2024-01-15 10:30:00');
        $additionalData = ['key' => 'value'];

        // Act
        $metadata = new Metadata(
            performedBy: $performedBy,
            ipAddress: $ipAddress,
            userAgent: $userAgent,
            url: $url,
            occurredAt: $occurredAt,
            additionalData: $additionalData
        );

        // Assert
        $this->assertEquals($performedBy, $metadata->getPerformedBy());
        $this->assertEquals($ipAddress, $metadata->getIpAddress());
        $this->assertEquals($userAgent, $metadata->getUserAgent());
        $this->assertEquals($url, $metadata->getUrl());
        $this->assertEquals($occurredAt, $metadata->getOccurredAt());
        $this->assertEquals($additionalData, $metadata->getAdditionalData());
    }

    /**
     * @test
     */
    public function it_should_create_metadata_with_default_values(): void
    {
        // Arrange & Act
        $metadata = new Metadata();

        // Assert
        $this->assertNull($metadata->getPerformedBy());
        $this->assertNull($metadata->getIpAddress());
        $this->assertNull($metadata->getUserAgent());
        $this->assertNull($metadata->getUrl());
        $this->assertInstanceOf(Carbon::class, $metadata->getOccurredAt());
        $this->assertEquals('2024-01-15 10:30:00', $metadata->getOccurredAt()->format('Y-m-d H:i:s'));
        $this->assertEquals([], $metadata->getAdditionalData());
    }

    /**
     * @test
     */
    public function it_should_create_metadata_from_request(): void
    {
        // Note: This test might need to be adjusted based on how auth() and request() are mocked
        // For now, we'll test the structure of the method

        // Arrange & Act
        $metadata = Metadata::fromRequest();

        // Assert
        $this->assertInstanceOf(Metadata::class, $metadata);
        $this->assertIsArray($metadata->getAdditionalData());
        $this->assertEquals('http', $metadata->getAdditionalData()['source']);
    }

    /**
     * @test
     */
    public function it_should_create_metadata_from_system_context(): void
    {
        // Arrange
        $context = 'COMMAND';
        $additionalData = ['command' => 'wallet:reconcile', 'batch_size' => 100];

        // Act
        $metadata = Metadata::fromSystem($context, $additionalData);

        // Assert
        $this->assertInstanceOf(Metadata::class, $metadata);
        $this->assertEquals(['type' => 'system', 'id' => 'COMMAND'], $metadata->getPerformedBy());
        $this->assertEquals('2024-01-15 10:30:00', $metadata->getOccurredAt()->format('Y-m-d H:i:s'));
        
        $expectedAdditionalData = [
            'source' => 'system',
            'context' => 'COMMAND',
            'command' => 'wallet:reconcile',
            'batch_size' => 100,
        ];
        $this->assertEquals($expectedAdditionalData, $metadata->getAdditionalData());
    }

    /**
     * @test
     */
    public function it_should_create_manual_metadata(): void
    {
        // Arrange
        $data = [
            'performed_by' => ['type' => 'manual', 'id' => 'TEST_USER'],
            'ip_address' => '127.0.0.1',
            'user_agent' => 'PHPUnit/Test',
            'url' => 'http://localhost/test',
            'occurred_at' => '2024-01-15 09:00:00',
            'additional_data' => ['test_scenario' => 'balance_overflow'],
        ];

        // Act
        $metadata = Metadata::manual($data);

        // Assert
        $this->assertEquals(['type' => 'manual', 'id' => 'TEST_USER'], $metadata->getPerformedBy());
        $this->assertEquals('127.0.0.1', $metadata->getIpAddress());
        $this->assertEquals('PHPUnit/Test', $metadata->getUserAgent());
        $this->assertEquals('http://localhost/test', $metadata->getUrl());
        $this->assertEquals('2024-01-15 09:00:00', $metadata->getOccurredAt()->format('Y-m-d H:i:s'));
        
        $expectedAdditionalData = [
            'source' => 'manual',
            'test_scenario' => 'balance_overflow',
        ];
        $this->assertEquals($expectedAdditionalData, $metadata->getAdditionalData());
    }

    /**
     * @test
     */
    public function it_should_create_manual_metadata_with_defaults(): void
    {
        // Arrange & Act
        $metadata = Metadata::manual();

        // Assert
        $this->assertEquals(['type' => 'manual', 'id' => 'MANUAL'], $metadata->getPerformedBy());
        $this->assertNull($metadata->getIpAddress());
        $this->assertNull($metadata->getUserAgent());
        $this->assertNull($metadata->getUrl());
        $this->assertEquals('2024-01-15 10:30:00', $metadata->getOccurredAt()->format('Y-m-d H:i:s'));
        $this->assertEquals(['source' => 'manual'], $metadata->getAdditionalData());
    }

    /**
     * @test
     */
    public function it_should_validate_ip_address(): void
    {
        // Valid IP addresses should work
        $validIPs = ['***********', '********', '127.0.0.1', '2001:db8::1'];
        
        foreach ($validIPs as $ip) {
            $metadata = new Metadata(ipAddress: $ip);
            $this->assertEquals($ip, $metadata->getIpAddress());
        }
    }

    /**
     * @test
     */
    public function it_should_throw_exception_for_invalid_ip_address(): void
    {
        // Arrange
        $this->expectException(InvalidMetadataException::class);
        $this->expectExceptionMessage('Invalid IP address: invalid.ip.address');

        // Act
        new Metadata(ipAddress: 'invalid.ip.address');
    }

    /**
     * @test
     */
    public function it_should_validate_url(): void
    {
        // Valid URLs should work
        $validUrls = [
            'https://example.com',
            'http://localhost:8080/path',
            'ftp://ftp.example.com/file.txt',
        ];
        
        foreach ($validUrls as $url) {
            $metadata = new Metadata(url: $url);
            $this->assertEquals($url, $metadata->getUrl());
        }
    }

    /**
     * @test
     */
    public function it_should_throw_exception_for_invalid_url(): void
    {
        // Arrange
        $this->expectException(InvalidMetadataException::class);
        $this->expectExceptionMessage('Invalid URL: not-a-valid-url');

        // Act
        new Metadata(url: 'not-a-valid-url');
    }

    /**
     * @test
     */
    public function it_should_validate_performed_by_structure(): void
    {
        // Valid performedBy structures
        $validPerformedBy = [
            ['type' => 'user', 'id' => '123'],
            ['type' => 'system', 'id' => 'COMMAND'],
            ['type' => 'manual', 'id' => 'TEST'],
        ];
        
        foreach ($validPerformedBy as $performedBy) {
            $metadata = new Metadata(performedBy: $performedBy);
            $this->assertEquals($performedBy, $metadata->getPerformedBy());
        }
    }

    /**
     * @test
     */
    public function it_should_throw_exception_for_missing_performed_by_keys(): void
    {
        // Arrange
        $this->expectException(InvalidMetadataException::class);
        $this->expectExceptionMessage('performedBy must have \'type\' and \'id\' keys');

        // Act
        new Metadata(performedBy: ['type' => 'user']); // Missing 'id'
    }

    /**
     * @test
     */
    public function it_should_throw_exception_for_invalid_performed_by_types(): void
    {
        // Arrange
        $this->expectException(InvalidMetadataException::class);
        $this->expectExceptionMessage('performedBy \'type\' and \'id\' must be strings');

        // Act
        new Metadata(performedBy: ['type' => 123, 'id' => 'test']); // Invalid type
    }

    /**
     * @test
     */
    public function it_should_throw_exception_for_invalid_performed_by_type_value(): void
    {
        // Arrange
        $this->expectException(InvalidMetadataException::class);
        $this->expectExceptionMessage('performedBy \'type\' must be one of: user, system, manual');

        // Act
        new Metadata(performedBy: ['type' => 'invalid', 'id' => 'test']);
    }

    /**
     * @test
     */
    public function it_should_allow_null_values_for_optional_fields(): void
    {
        // Arrange & Act
        $metadata = new Metadata(
            performedBy: null,
            ipAddress: null,
            userAgent: null,
            url: null
        );

        // Assert
        $this->assertNull($metadata->getPerformedBy());
        $this->assertNull($metadata->getIpAddress());
        $this->assertNull($metadata->getUserAgent());
        $this->assertNull($metadata->getUrl());
    }

    /**
     * @test
     */
    public function it_should_create_new_instance_with_additional_data(): void
    {
        // Arrange
        $originalMetadata = new Metadata(
            performedBy: ['type' => 'user', 'id' => '123'],
            additionalData: ['original' => 'data']
        );
        $newData = ['new' => 'data', 'another' => 'value'];

        // Act
        $newMetadata = $originalMetadata->withAdditionalData($newData);

        // Assert
        $this->assertNotSame($originalMetadata, $newMetadata);
        $this->assertEquals(['original' => 'data'], $originalMetadata->getAdditionalData());
        $this->assertEquals([
            'original' => 'data',
            'new' => 'data',
            'another' => 'value',
        ], $newMetadata->getAdditionalData());
        
        // Other properties should remain the same
        $this->assertEquals($originalMetadata->getPerformedBy(), $newMetadata->getPerformedBy());
    }

    /**
     * @test
     */
    public function it_should_convert_to_array(): void
    {
        // Arrange
        $occurredAt = Carbon::parse('2024-01-15 10:30:00');
        $metadata = new Metadata(
            performedBy: ['type' => 'user', 'id' => '123'],
            ipAddress: '***********',
            userAgent: 'Mozilla/5.0',
            url: 'https://example.com',
            occurredAt: $occurredAt,
            additionalData: ['key' => 'value']
        );

        // Act
        $array = $metadata->toArray();

        // Assert
        $this->assertIsArray($array);
        $this->assertEquals(['type' => 'user', 'id' => '123'], $array['performed_by']);
        $this->assertEquals('***********', $array['ip_address']);
        $this->assertEquals('Mozilla/5.0', $array['user_agent']);
        $this->assertEquals('https://example.com', $array['url']);
        $this->assertEquals($occurredAt->toISOString(), $array['occurred_at']);
        $this->assertEquals(['key' => 'value'], $array['additional_data']);
    }

    /**
     * @test
     */
    public function it_should_be_json_serializable(): void
    {
        // Arrange
        $metadata = new Metadata(
            performedBy: ['type' => 'user', 'id' => '123'],
            ipAddress: '***********',
            additionalData: ['key' => 'value']
        );

        // Act
        $json = json_encode($metadata);
        $decoded = json_decode($json, true);

        // Assert
        $this->assertJson($json);
        $this->assertEquals($metadata->toArray(), $decoded);
    }

    /**
     * @test
     * @dataProvider systemContextProvider
     */
    public function it_should_handle_various_system_contexts(string $context, array $additionalData): void
    {
        // Arrange & Act
        $metadata = Metadata::fromSystem($context, $additionalData);

        // Assert
        $this->assertEquals(['type' => 'system', 'id' => strtoupper($context)], $metadata->getPerformedBy());
        $this->assertArrayHasKey('source', $metadata->getAdditionalData());
        $this->assertArrayHasKey('context', $metadata->getAdditionalData());
        $this->assertEquals('system', $metadata->getAdditionalData()['source']);
        $this->assertEquals($context, $metadata->getAdditionalData()['context']);
        
        foreach ($additionalData as $key => $value) {
            $this->assertArrayHasKey($key, $metadata->getAdditionalData());
            $this->assertEquals($value, $metadata->getAdditionalData()[$key]);
        }
    }

    /**
     * Data provider for system context testing.
     */
    public static function systemContextProvider(): array
    {
        return [
            'command_context' => [
                'COMMAND',
                ['command' => 'wallet:reconcile', 'arguments' => ['--batch-size=100']],
            ],
            'job_context' => [
                'JOB',
                ['job' => 'ProcessWalletTransaction', 'queue' => 'payments', 'attempt' => 1],
            ],
            'observer_context' => [
                'OBSERVER',
                ['observer' => 'WalletAccountObserver', 'event' => 'created'],
            ],
            'saga_context' => [
                'SAGA',
                ['saga' => 'TransactionProcessingSaga', 'step' => 'ValidateBalance'],
            ],
            'scheduler_context' => [
                'SCHEDULER',
                ['task' => 'daily-balance-report', 'schedule' => '0 2 * * *'],
            ],
        ];
    }

    /**
     * @test
     */
    public function it_should_handle_edge_cases_in_manual_metadata(): void
    {
        // Arrange
        $edgeCaseData = [
            'performed_by' => ['type' => 'manual', 'id' => 'INTEGRATION_TEST'],
            'additional_data' => [
                'test_case' => 'WalletBalanceTest::testLargeDeposit',
                'environment' => 'testing',
                'nested' => ['deep' => ['value' => 'test']],
            ],
        ];

        // Act
        $metadata = Metadata::manual($edgeCaseData);

        // Assert
        $this->assertEquals(['type' => 'manual', 'id' => 'INTEGRATION_TEST'], $metadata->getPerformedBy());
        $expectedAdditionalData = [
            'source' => 'manual',
            'test_case' => 'WalletBalanceTest::testLargeDeposit',
            'environment' => 'testing',
            'nested' => ['deep' => ['value' => 'test']],
        ];
        $this->assertEquals($expectedAdditionalData, $metadata->getAdditionalData());
    }

    /**
     * @test
     */
    public function it_should_maintain_immutability(): void
    {
        // Arrange
        $originalData = ['original' => 'data'];
        $metadata = new Metadata(additionalData: $originalData);

        // Act
        $newMetadata = $metadata->withAdditionalData(['new' => 'data']);
        
        // Modify original array to test immutability
        $originalData['modified'] = 'after_creation';

        // Assert
        $this->assertEquals(['original' => 'data'], $metadata->getAdditionalData());
        $this->assertEquals(['original' => 'data', 'new' => 'data'], $newMetadata->getAdditionalData());
        $this->assertNotContains('modified', $metadata->getAdditionalData());
    }
}