<?php

declare(strict_types=1);

namespace Thorne\Wallet\Tests\Unit\Domain\ValueObjects;

use Thorne\Wallet\Domain\Exceptions\UnsupportedCurrencyException;
use Thorne\Wallet\Domain\ValueObjects\Currency;

/**
 * @covers \Thorne\Wallet\Domain\ValueObjects\Currency
 */
final class CurrencyTest extends ValueObjectTestCase
{
    /**
     * @test
     */
    public function it_should_create_currency_from_valid_code(): void
    {
        // Arrange & Act
        $currency = Currency::from('EUR');

        // Assert
        $this->assertInstanceOf(Currency::class, $currency);
        $this->assertEquals('EUR', $currency->getCode());
    }

    /**
     * @test
     */
    public function it_should_normalize_currency_code_to_uppercase(): void
    {
        // Arrange & Act
        $currency = Currency::from('eur');

        // Assert
        $this->assertEquals('EUR', $currency->getCode());
    }

    /**
     * @test
     */
    public function it_should_throw_exception_for_unsupported_currency(): void
    {
        // Arrange
        $this->expectException(UnsupportedCurrencyException::class);
        $this->expectExceptionMessage('Currency not supported: INVALID');

        // Act
        Currency::from('INVALID');
    }

    /**
     * @test
     */
    public function it_should_throw_exception_when_currency_config_not_found(): void
    {
        // Arrange
        $this->expectException(UnsupportedCurrencyException::class);
        $this->expectExceptionMessage('Currency not supported: XYZ');

        // Act - This should trigger the null config scenario
        Currency::from('XYZ'); // Assuming XYZ is not in config
    }

    /**
     * @test
     */
    public function it_should_get_default_currency(): void
    {
        // Arrange & Act
        $defaultCurrency = Currency::getDefault();

        // Assert
        $this->assertInstanceOf(Currency::class, $defaultCurrency);
        $this->assertTrue($defaultCurrency->isDefault());
    }

    /**
     * @test
     */
    public function it_should_throw_exception_when_no_default_currency_configured(): void
    {
        // Arrange - Mock WalletConfig to return null for default currency
        \Mockery::close(); // Close existing mocks

        \Thorne\Wallet\Support\Helpers\WalletConfig::shouldReceive('getDefaultCurrency')
            ->andReturn(null);

        $this->expectException(UnsupportedCurrencyException::class);
        $this->expectExceptionMessage('No default currency configured');

        // Act
        Currency::getDefault();
    }

    /**
     * @test
     */
    public function it_should_get_all_supported_currencies(): void
    {
        // Arrange & Act
        $currencies = Currency::getAllSupported();

        // Assert
        $this->assertIsArray($currencies);
        $this->assertNotEmpty($currencies);
        foreach ($currencies as $currency) {
            $this->assertInstanceOf(Currency::class, $currency);
        }
    }

    /**
     * @test
     */
    public function it_should_return_currency_name(): void
    {
        // Arrange
        $currency = Currency::from('EUR');

        // Act & Assert
        $this->assertIsString($currency->getName());
        $this->assertNotEmpty($currency->getName());
    }

    /**
     * @test
     */
    public function it_should_return_currency_symbol(): void
    {
        // Arrange
        $currency = Currency::from('EUR');

        // Act & Assert
        $this->assertIsString($currency->getSymbol());
        $this->assertNotEmpty($currency->getSymbol());
    }

    /**
     * @test
     */
    public function it_should_return_currency_iso_code(): void
    {
        // Arrange
        $currency = Currency::from('EUR');

        // Act & Assert
        $this->assertIsInt($currency->getIsoCode());
        $this->assertEquals(978, $currency->getIsoCode()); // EUR ISO code
    }

    /**
     * @test
     */
    public function it_should_return_currency_precision(): void
    {
        // Arrange
        $currency = Currency::from('EUR');

        // Act & Assert
        $this->assertIsInt($currency->getPrecision());
        $this->assertGreaterThanOrEqual(0, $currency->getPrecision());
    }

    /**
     * @test
     */
    public function it_should_return_default_precision_when_not_configured(): void
    {
        // Arrange
        $currency = Currency::from('EUR'); // Assuming EUR has precision configured

        // Act & Assert
        $this->assertIsInt($currency->getPrecision());
        // This test verifies the fallback to 2 when precision is not in config
    }

    /**
     * @test
     */
    public function it_should_check_if_currency_is_enabled(): void
    {
        // Arrange
        $currency = Currency::from('EUR');

        // Act & Assert
        $this->assertIsBool($currency->isEnabled());
    }

    /**
     * @test
     */
    public function it_should_return_false_for_enabled_when_not_configured(): void
    {
        // Arrange
        $currency = Currency::from('EUR'); // Test fallback behavior

        // Act & Assert
        $this->assertIsBool($currency->isEnabled());
        // This test verifies the fallback to false when enabled is not in config
    }

    /**
     * @test
     */
    public function it_should_check_if_currency_is_default(): void
    {
        // Arrange
        $defaultCurrency = Currency::getDefault();
        $nonDefaultCurrency = Currency::from('USD'); // Assuming USD is not default

        // Act & Assert
        $this->assertTrue($defaultCurrency->isDefault());
        $this->assertFalse($nonDefaultCurrency->isDefault());
    }

    /**
     * @test
     */
    public function it_should_check_if_currency_is_custom(): void
    {
        // Arrange
        $currency = Currency::from('EUR');

        // Act & Assert
        $this->assertIsBool($currency->isCustom());
    }

    /**
     * @test
     */
    public function it_should_return_false_for_custom_when_not_configured(): void
    {
        // Arrange
        $currency = Currency::from('EUR'); // Test fallback behavior

        // Act & Assert
        $this->assertIsBool($currency->isCustom());
        // This test verifies the fallback to false when is_custom is not in config
    }

    /**
     * @test
     */
    public function it_should_check_if_currency_is_supported(): void
    {
        // Arrange
        $currency = Currency::from('EUR');

        // Act & Assert
        $this->assertTrue($currency->isSupported());
    }

    /**
     * @test
     */
    public function it_should_check_equality_between_currencies(): void
    {
        // Arrange
        $currency1 = Currency::from('EUR');
        $currency2 = Currency::from('EUR');
        $currency3 = Currency::from('USD');

        // Act & Assert
        $this->assertTrue($currency1->equals($currency2));
        $this->assertFalse($currency1->equals($currency3));
    }

    /**
     * @test
     */
    public function it_should_convert_to_array(): void
    {
        // Arrange
        $currency = Currency::from('EUR');

        // Act
        $array = $currency->toArray();

        // Assert
        $this->assertIsArray($array);
        $this->assertArrayHasKey('code', $array);
        $this->assertArrayHasKey('name', $array);
        $this->assertArrayHasKey('symbol', $array);
        $this->assertArrayHasKey('iso_code', $array);
        $this->assertArrayHasKey('precision', $array);
        $this->assertArrayHasKey('enabled', $array);
        $this->assertArrayHasKey('is_default', $array);
        $this->assertArrayHasKey('is_custom', $array);
        
        $this->assertEquals('EUR', $array['code']);
        $this->assertEquals(978, $array['iso_code']);
    }

    /**
     * @test
     */
    public function it_should_convert_to_string(): void
    {
        // Arrange
        $currency = Currency::from('EUR');

        // Act & Assert
        $this->assertEquals('EUR', $currency->toString());
        $this->assertEquals('EUR', (string) $currency);
    }

    /**
     * @test
     */
    public function it_should_be_json_serializable(): void
    {
        // Arrange
        $currency = Currency::from('EUR');

        // Act
        $json = json_encode($currency);
        $decoded = json_decode($json, true);

        // Assert
        $this->assertJson($json);
        $this->assertEquals($currency->toArray(), $decoded);
    }

    /**
     * @test
     */
    public function it_should_handle_multiple_supported_currencies(): void
    {
        // Arrange
        $supportedCurrencies = ['EUR', 'USD', 'TRY'];

        foreach ($supportedCurrencies as $currencyCode) {
            // Act
            $currency = Currency::from($currencyCode);

            // Assert
            $this->assertInstanceOf(Currency::class, $currency);
            $this->assertEquals($currencyCode, $currency->getCode());
            $this->assertTrue($currency->isSupported());
        }
    }

    /**
     * @test
     */
    public function it_should_return_fallback_values_for_missing_config_properties(): void
    {
        // Arrange
        $currency = Currency::from('EUR');

        // Act & Assert - Test fallback behaviors
        $name = $currency->getName();
        $symbol = $currency->getSymbol();
        
        // Should return code as fallback if name/symbol not configured
        $this->assertIsString($name);
        $this->assertIsString($symbol);
        
        // If config missing, should return code as fallback
        if ($name === 'EUR') {
            $this->assertEquals('EUR', $name); // Fallback to code
        }
        
        if ($symbol === 'EUR') {
            $this->assertEquals('EUR', $symbol); // Fallback to code
        }
    }

    /**
     * @test
     */
    public function it_should_maintain_immutability(): void
    {
        // Arrange
        $currency = Currency::from('EUR');
        $originalCode = $currency->getCode();

        // Act - Try to modify (this should not be possible, but we test the behavior)
        $sameReference = $currency;

        // Assert
        $this->assertEquals($originalCode, $sameReference->getCode());
        $this->assertSame($currency, $sameReference);
    }

    /**
     * @test
     * @dataProvider currencyCodeProvider
     */
    public function it_should_handle_various_currency_code_formats(string $input, string $expected): void
    {
        // Arrange & Act
        $currency = Currency::from($input);

        // Assert
        $this->assertEquals($expected, $currency->getCode());
    }

    /**
     * Data provider for currency code format testing.
     */
    public static function currencyCodeProvider(): array
    {
        return [
            'uppercase' => ['EUR', 'EUR'],
            'lowercase' => ['eur', 'EUR'],
            'mixed_case' => ['EuR', 'EUR'],
            'usd_uppercase' => ['USD', 'USD'],
            'usd_lowercase' => ['usd', 'USD'],
        ];
    }

    /**
     * @test
     */
    public function it_should_handle_edge_cases_in_configuration(): void
    {
        // Arrange
        $currency = Currency::from('EUR');

        // Act & Assert - Test edge cases
        $isoCode = $currency->getIsoCode();
        $precision = $currency->getPrecision();
        
        $this->assertIsInt($isoCode);
        $this->assertGreaterThanOrEqual(0, $isoCode);
        $this->assertIsInt($precision);
        $this->assertGreaterThanOrEqual(0, $precision);
        $this->assertLessThanOrEqual(8, $precision); // Reasonable upper bound for precision
    }
}