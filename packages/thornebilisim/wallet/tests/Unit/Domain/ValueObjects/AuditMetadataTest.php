<?php

declare(strict_types=1);

namespace Thorne\Wallet\Tests\Unit\Domain\ValueObjects;

use PHPUnit\Framework\TestCase;
use Thorne\Wallet\Domain\Exceptions\WalletAuditException;
use Thorne\Wallet\Domain\ValueObjects\AuditMetadata;

/**
 * @covers \Thorne\Wallet\Domain\ValueObjects\AuditMetadata
 */
final class AuditMetadataTest extends TestCase
{
    /**
     * @test
     */
    public function it_should_create_audit_metadata_with_all_parameters(): void
    {
        // Arrange
        $userAgent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)';
        $url = 'https://example.com/api/test';
        $method = 'POST';
        $headers = ['Content-Type' => 'application/json', 'Authorization' => 'Bearer token'];
        $requestData = ['amount' => '100.00', 'currency' => 'EUR'];
        $sessionId = 'session_123';
        $deviceId = 'device_456';
        $customData = ['trace_id' => 'trace_789'];

        // Act
        $auditMetadata = new AuditMetadata(
            userAgent: $userAgent,
            url: $url,
            method: $method,
            headers: $headers,
            requestData: $requestData,
            sessionId: $sessionId,
            deviceId: $deviceId,
            customData: $customData
        );

        // Assert
        $this->assertEquals($userAgent, $auditMetadata->userAgent);
        $this->assertEquals($url, $auditMetadata->url);
        $this->assertEquals($method, $auditMetadata->method);
        $this->assertEquals($headers, $auditMetadata->headers);
        $this->assertEquals($requestData, $auditMetadata->requestData);
        $this->assertEquals($sessionId, $auditMetadata->sessionId);
        $this->assertEquals($deviceId, $auditMetadata->deviceId);
        $this->assertEquals($customData, $auditMetadata->customData);
    }

    /**
     * @test
     */
    public function it_should_create_audit_metadata_with_default_null_values(): void
    {
        // Arrange & Act
        $auditMetadata = new AuditMetadata();

        // Assert
        $this->assertNull($auditMetadata->userAgent);
        $this->assertNull($auditMetadata->url);
        $this->assertNull($auditMetadata->method);
        $this->assertNull($auditMetadata->headers);
        $this->assertNull($auditMetadata->requestData);
        $this->assertNull($auditMetadata->sessionId);
        $this->assertNull($auditMetadata->deviceId);
        $this->assertNull($auditMetadata->customData);
    }

    /**
     * @test
     */
    public function it_should_create_audit_metadata_from_request(): void
    {
        // Arrange
        $userAgent = 'Mozilla/5.0';
        $url = 'https://example.com/api/wallet';
        $method = 'POST';
        $headers = ['Content-Type' => 'application/json'];
        $requestData = ['amount' => '100.00'];
        $sessionId = 'sess_123';

        // Act
        $auditMetadata = AuditMetadata::fromRequest(
            userAgent: $userAgent,
            url: $url,
            method: $method,
            headers: $headers,
            requestData: $requestData,
            sessionId: $sessionId
        );

        // Assert
        $this->assertInstanceOf(AuditMetadata::class, $auditMetadata);
        $this->assertEquals($userAgent, $auditMetadata->userAgent);
        $this->assertEquals($url, $auditMetadata->url);
        $this->assertEquals($method, $auditMetadata->method);
        $this->assertEquals($headers, $auditMetadata->headers);
        $this->assertEquals($requestData, $auditMetadata->requestData);
        $this->assertEquals($sessionId, $auditMetadata->sessionId);
    }

    /**
     * @test
     */
    public function it_should_create_audit_metadata_from_system(): void
    {
        // Arrange
        $source = 'background_job';
        $customData = ['job_id' => 'job_123', 'queue' => 'high_priority'];

        // Act
        $auditMetadata = AuditMetadata::fromSystem($source, $customData);

        // Assert
        $this->assertInstanceOf(AuditMetadata::class, $auditMetadata);
        $expectedCustomData = [
            'source' => 'background_job',
            'job_id' => 'job_123',
            'queue' => 'high_priority',
        ];
        $this->assertEquals($expectedCustomData, $auditMetadata->customData);
    }

    /**
     * @test
     */
    public function it_should_create_audit_metadata_from_system_with_default_source(): void
    {
        // Arrange & Act
        $auditMetadata = AuditMetadata::fromSystem();

        // Assert
        $this->assertEquals(['source' => 'system'], $auditMetadata->customData);
    }

    /**
     * @test
     */
    public function it_should_create_audit_metadata_from_api(): void
    {
        // Arrange
        $userAgent = 'API Client/1.0';
        $endpoint = '/api/v1/wallets';
        $method = 'GET';
        $headers = ['X-API-Version' => '1.0'];
        $apiKey = 'api_key_secret_123';

        // Act
        $auditMetadata = AuditMetadata::fromApi(
            userAgent: $userAgent,
            endpoint: $endpoint,
            method: $method,
            headers: $headers,
            apiKey: $apiKey
        );

        // Assert
        $this->assertInstanceOf(AuditMetadata::class, $auditMetadata);
        $this->assertEquals($userAgent, $auditMetadata->userAgent);
        $this->assertEquals($endpoint, $auditMetadata->url);
        $this->assertEquals($method, $auditMetadata->method);
        $this->assertEquals($headers, $auditMetadata->headers);
        
        // API key should be hashed in custom data
        $this->assertArrayHasKey('api_key_hash', $auditMetadata->customData);
        $this->assertEquals(hash('sha256', $apiKey), $auditMetadata->customData['api_key_hash']);
    }

    /**
     * @test
     */
    public function it_should_create_audit_metadata_from_api_without_api_key(): void
    {
        // Arrange & Act
        $auditMetadata = AuditMetadata::fromApi(
            userAgent: 'API Client/1.0',
            endpoint: '/api/v1/wallets'
        );

        // Assert
        $this->assertInstanceOf(AuditMetadata::class, $auditMetadata);
        $this->assertEmpty($auditMetadata->customData);
    }

    /**
     * @test
     */
    public function it_should_create_empty_audit_metadata(): void
    {
        // Arrange & Act
        $auditMetadata = AuditMetadata::empty();

        // Assert
        $this->assertInstanceOf(AuditMetadata::class, $auditMetadata);
        $this->assertNull($auditMetadata->userAgent);
        $this->assertNull($auditMetadata->url);
        $this->assertNull($auditMetadata->method);
        $this->assertNull($auditMetadata->headers);
        $this->assertNull($auditMetadata->requestData);
        $this->assertNull($auditMetadata->sessionId);
        $this->assertNull($auditMetadata->deviceId);
        $this->assertNull($auditMetadata->customData);
    }

    /**
     * @test
     */
    public function it_should_throw_exception_for_metadata_too_large(): void
    {
        // Arrange
        $largeData = array_fill(0, 10000, str_repeat('x', 100)); // Create very large data
        
        $this->expectException(WalletAuditException::class);
        $this->expectExceptionMessage('metadata too large');

        // Act
        new AuditMetadata(customData: $largeData);
    }

    /**
     * @test
     */
    public function it_should_throw_exception_for_invalid_url_format(): void
    {
        // Arrange
        $this->expectException(WalletAuditException::class);
        $this->expectExceptionMessage('invalid URL format');

        // Act
        new AuditMetadata(url: 'not-a-valid-url');
    }

    /**
     * @test
     */
    public function it_should_allow_relative_urls_starting_with_slash(): void
    {
        // Arrange & Act
        $auditMetadata = new AuditMetadata(url: '/api/v1/wallets');

        // Assert
        $this->assertEquals('/api/v1/wallets', $auditMetadata->url);
    }

    /**
     * @test
     */
    public function it_should_throw_exception_for_invalid_http_method(): void
    {
        // Arrange
        $this->expectException(WalletAuditException::class);
        $this->expectExceptionMessage('invalid HTTP method');

        // Act
        new AuditMetadata(method: 'INVALID');
    }

    /**
     * @test
     * @dataProvider validHttpMethodsProvider
     */
    public function it_should_accept_valid_http_methods(string $method): void
    {
        // Arrange & Act
        $auditMetadata = new AuditMetadata(method: $method);

        // Assert
        $this->assertEquals($method, $auditMetadata->method);
    }

    /**
     * Data provider for valid HTTP methods.
     */
    public static function validHttpMethodsProvider(): array
    {
        return [
            ['GET'],
            ['POST'],
            ['PUT'],
            ['PATCH'],
            ['DELETE'],
            ['HEAD'],
            ['OPTIONS'],
            ['get'], // Should be normalized to uppercase
            ['post'],
        ];
    }

    /**
     * @test
     */
    public function it_should_check_if_metadata_is_security_relevant(): void
    {
        // Arrange
        $adminUrl = new AuditMetadata(url: 'https://example.com/admin/dashboard');
        $regularUrl = new AuditMetadata(url: 'https://example.com/api/public');
        $postMethod = new AuditMetadata(method: 'POST');
        $getMethod = new AuditMetadata(method: 'GET');

        // Act & Assert
        $this->assertTrue($adminUrl->isSecurityRelevant());
        $this->assertFalse($regularUrl->isSecurityRelevant());
        $this->assertTrue($postMethod->isSecurityRelevant());
        $this->assertFalse($getMethod->isSecurityRelevant());
    }

    /**
     * @test
     */
    public function it_should_sanitize_headers_removing_sensitive_information(): void
    {
        // Arrange
        $headers = [
            'Content-Type' => 'application/json',
            'Authorization' => 'Bearer secret_token',
            'Cookie' => 'session=abc123; user=john',
            'X-API-Key' => 'api_secret_key',
            'X-Auth-Token' => 'auth_token_123',
            'X-CSRF-Token' => 'csrf_token_456',
            'User-Agent' => 'Mozilla/5.0',
            'Accept' => 'application/json',
        ];

        $auditMetadata = new AuditMetadata(headers: $headers);

        // Act
        $sanitizedHeaders = $auditMetadata->getSanitizedHeaders();

        // Assert
        $this->assertEquals('application/json', $sanitizedHeaders['Content-Type']);
        $this->assertEquals('[REDACTED]', $sanitizedHeaders['Authorization']);
        $this->assertEquals('[REDACTED]', $sanitizedHeaders['Cookie']);
        $this->assertEquals('[REDACTED]', $sanitizedHeaders['X-API-Key']);
        $this->assertEquals('[REDACTED]', $sanitizedHeaders['X-Auth-Token']);
        $this->assertEquals('[REDACTED]', $sanitizedHeaders['X-CSRF-Token']);
        $this->assertEquals('Mozilla/5.0', $sanitizedHeaders['User-Agent']);
        $this->assertEquals('application/json', $sanitizedHeaders['Accept']);
    }

    /**
     * @test
     */
    public function it_should_return_null_for_sanitized_headers_when_no_headers(): void
    {
        // Arrange
        $auditMetadata = new AuditMetadata();

        // Act
        $sanitizedHeaders = $auditMetadata->getSanitizedHeaders();

        // Assert
        $this->assertNull($sanitizedHeaders);
    }

    /**
     * @test
     */
    public function it_should_convert_to_array_filtering_null_values(): void
    {
        // Arrange
        $auditMetadata = new AuditMetadata(
            userAgent: 'Mozilla/5.0',
            url: 'https://example.com',
            method: null,
            headers: ['Content-Type' => 'application/json'],
            requestData: null,
            sessionId: 'sess_123',
            deviceId: null,
            customData: ['key' => 'value']
        );

        // Act
        $array = $auditMetadata->toArray();

        // Assert
        $this->assertIsArray($array);
        $this->assertEquals('Mozilla/5.0', $array['user_agent']);
        $this->assertEquals('https://example.com', $array['url']);
        $this->assertArrayNotHasKey('method', $array); // Null values filtered out
        $this->assertEquals(['Content-Type' => '[REDACTED]'], $array['headers']); // Headers are sanitized
        $this->assertArrayNotHasKey('request_data', $array); // Null values filtered out
        $this->assertEquals('sess_123', $array['session_id']);
        $this->assertArrayNotHasKey('device_id', $array); // Null values filtered out
        $this->assertEquals(['key' => 'value'], $array['custom_data']);
    }

    /**
     * @test
     */
    public function it_should_be_json_serializable(): void
    {
        // Arrange
        $auditMetadata = new AuditMetadata(
            userAgent: 'Mozilla/5.0',
            url: 'https://example.com',
            sessionId: 'sess_123'
        );

        // Act
        $json = json_encode($auditMetadata);
        $decoded = json_decode($json, true);

        // Assert
        $this->assertJson($json);
        $this->assertEquals('Mozilla/5.0', $decoded['userAgent']);
        $this->assertEquals('https://example.com', $decoded['url']);
        $this->assertEquals('sess_123', $decoded['sessionId']);
    }

    /**
     * @test
     */
    public function it_should_create_from_array(): void
    {
        // Arrange
        $data = [
            'user_agent' => 'Mozilla/5.0',
            'url' => 'https://example.com',
            'method' => 'POST',
            'headers' => ['Content-Type' => 'application/json'],
            'request_data' => ['amount' => '100.00'],
            'session_id' => 'sess_123',
            'device_id' => 'device_456',
            'custom_data' => ['key' => 'value'],
        ];

        // Act
        $auditMetadata = AuditMetadata::fromArray($data);

        // Assert
        $this->assertInstanceOf(AuditMetadata::class, $auditMetadata);
        $this->assertEquals('Mozilla/5.0', $auditMetadata->userAgent);
        $this->assertEquals('https://example.com', $auditMetadata->url);
        $this->assertEquals('POST', $auditMetadata->method);
        $this->assertEquals(['Content-Type' => 'application/json'], $auditMetadata->headers);
        $this->assertEquals(['amount' => '100.00'], $auditMetadata->requestData);
        $this->assertEquals('sess_123', $auditMetadata->sessionId);
        $this->assertEquals('device_456', $auditMetadata->deviceId);
        $this->assertEquals(['key' => 'value'], $auditMetadata->customData);
    }

    /**
     * @test
     */
    public function it_should_check_equality_with_another_audit_metadata(): void
    {
        // Arrange
        $auditMetadata1 = new AuditMetadata(
            userAgent: 'Mozilla/5.0',
            url: 'https://example.com'
        );
        $auditMetadata2 = new AuditMetadata(
            userAgent: 'Mozilla/5.0',
            url: 'https://example.com'
        );
        $auditMetadata3 = new AuditMetadata(
            userAgent: 'Different Agent',
            url: 'https://example.com'
        );

        // Act & Assert
        $this->assertTrue($auditMetadata1->equals($auditMetadata2));
        $this->assertFalse($auditMetadata1->equals($auditMetadata3));
    }

    /**
     * @test
     */
    public function it_should_get_summary_for_logging(): void
    {
        // Arrange
        $auditMetadata = new AuditMetadata(
            userAgent: 'Mozilla/5.0',
            url: '/api/v1/wallets',
            method: 'POST',
            headers: ['Authorization' => 'Bearer token'],
            customData: ['api_key_hash' => 'hashed_key']
        );

        // Act
        $summary = $auditMetadata->getSummary();

        // Assert
        $this->assertIsArray($summary);
        $this->assertEquals('Mozilla/5.0', $summary['user_agent']);
        $this->assertEquals('/api/v1/wallets', $summary['endpoint']);
        $this->assertEquals('POST', $summary['method']);
        $this->assertTrue($summary['has_auth']);
        $this->assertTrue($summary['has_api_key']);
    }

    /**
     * @test
     */
    public function it_should_convert_to_string_representation(): void
    {
        // Arrange
        $auditMetadata = new AuditMetadata(
            userAgent: 'Mozilla/5.0',
            method: 'POST',
            url: 'https://example.com/api'
        );

        // Act
        $string = (string) $auditMetadata;

        // Assert
        $this->assertEquals('User-Agent: Mozilla/5.0 | POST https://example.com/api', $string);
    }

    /**
     * @test
     */
    public function it_should_return_empty_metadata_string_when_no_data(): void
    {
        // Arrange
        $auditMetadata = new AuditMetadata();

        // Act
        $string = (string) $auditMetadata;

        // Assert
        $this->assertEquals('Empty metadata', $string);
    }

    /**
     * @test
     */
    public function it_should_handle_case_insensitive_header_sanitization(): void
    {
        // Arrange
        $headers = [
            'AUTHORIZATION' => 'Bearer token',
            'authorization' => 'Bearer token2',
            'Authorization' => 'Bearer token3',
            'Content-Type' => 'application/json',
        ];

        $auditMetadata = new AuditMetadata(headers: $headers);

        // Act
        $sanitizedHeaders = $auditMetadata->getSanitizedHeaders();

        // Assert
        $this->assertEquals('[REDACTED]', $sanitizedHeaders['AUTHORIZATION']);
        $this->assertEquals('[REDACTED]', $sanitizedHeaders['authorization']);
        $this->assertEquals('[REDACTED]', $sanitizedHeaders['Authorization']);
        $this->assertEquals('application/json', $sanitizedHeaders['Content-Type']);
    }

    /**
     * @test
     */
    public function it_should_handle_edge_cases_in_validation(): void
    {
        // Test valid edge cases that should not throw exceptions
        $validCases = [
            // Valid URLs
            new AuditMetadata(url: 'https://example.com'),
            new AuditMetadata(url: 'http://localhost:8080'),
            new AuditMetadata(url: '/relative/path'),
            
            // Valid HTTP methods (case insensitive)
            new AuditMetadata(method: 'GET'),
            new AuditMetadata(method: 'post'),
            new AuditMetadata(method: 'OPTIONS'),
            
            // Null values should be accepted
            new AuditMetadata(url: null, method: null),
        ];

        foreach ($validCases as $auditMetadata) {
            $this->assertInstanceOf(AuditMetadata::class, $auditMetadata);
        }
    }
}