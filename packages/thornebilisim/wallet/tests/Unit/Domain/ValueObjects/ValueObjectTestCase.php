<?php

declare(strict_types=1);

namespace Thorne\Wallet\Tests\Unit\Domain\ValueObjects;

use Thorne\Wallet\Tests\TestCase;

/**
 * Base test case for Value Object tests.
 *
 * Provides common test data and utilities for all Value Object tests.
 * Uses <PERSON><PERSON>'s TestCase to access config() helper for mocking configurations.
 */
abstract class ValueObjectTestCase extends TestCase
{
    protected function setUp(): void
    {
        parent::setUp();
        $this->mockWalletConfig();
    }

    /**
     * Mock wallet configuration for testing.
     */
    protected function mockWalletConfig(): void
    {
        config([
            'wallet.currencies' => [
                'EUR' => [
                    'code' => 'EUR',
                    'iso_code' => 978,
                    'name' => 'Euro',
                    'symbol' => '€',
                    'precision' => 2,
                    'enabled' => true,
                    'is_default' => true,
                    'is_custom' => false,
                ],
                'MLGR' => [
                    'code' => 'MLGR',
                    'iso_code' => 999,
                    'name' => 'miliGRAM',
                    'symbol' => 'MLGR',
                    'precision' => 0,
                    'enabled' => true,
                    'is_default' => false,
                    'is_custom' => true,
                ],
                'USD' => [
                    'code' => 'USD',
                    'iso_code' => 840,
                    'name' => 'US Dollar',
                    'symbol' => '$',
                    'precision' => 2,
                    'enabled' => false, // Disabled for testing
                    'is_default' => false,
                    'is_custom' => false,
                ],
            ],
        ]);
    }



    /**
     * Get valid account number with correct checksum for EUR.
     */
    protected function getValidEurAccountNumber(): string
    {
        // EUR ISO: 978, sequence: 1234567, calculated check digit: 2
        return '***********';
    }

    /**
     * Get valid account number with correct checksum for MLGR.
     */
    protected function getValidMlgrAccountNumber(): string
    {
        // MLGR ISO: 999, sequence: 1234567, calculated check digit: 7
        return '***********';
    }

    /**
     * Calculate Luhn check digit for testing.
     */
    protected function calculateLuhnCheckDigit(string $number): int
    {
        $sum = 0;
        $length = strlen($number);
        
        for ($i = 0; $i < $length; $i++) {
            $digit = (int) $number[$length - 1 - $i];
            
            if ($i % 2 === 0) { // Even position (from right)
                $digit *= 2;
                if ($digit > 9) {
                    $digit -= 9;
                }
            }
            
            $sum += $digit;
        }
        
        return (10 - ($sum % 10)) % 10;
    }

    /**
     * Generate valid account number for currency.
     */
    protected function generateValidAccountNumber(string $currencyCode): string
    {
        $currencies = [
            'EUR' => 978,
            'MLGR' => 999,
            'USD' => 840,
        ];

        $isoCode = str_pad((string) $currencies[$currencyCode], 3, '0', STR_PAD_LEFT);
        $sequence = '1234567'; // Fixed sequence for testing
        $baseNumber = $isoCode . $sequence;
        $checkDigit = $this->calculateLuhnCheckDigit($baseNumber);
        
        return $baseNumber . $checkDigit;
    }
}
