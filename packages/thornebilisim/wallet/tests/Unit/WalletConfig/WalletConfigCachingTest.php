<?php

declare(strict_types=1);

namespace Thorne\Wallet\Tests\Unit\WalletConfig;

use <PERSON>\Wallet\Support\Helpers\WalletConfig;
use <PERSON>\Wallet\Tests\TestCase;

/**
 * Unit tests for WalletConfig Helper - Caching Mechanism
 *
 * Component Type: Configuration Helper
 * Test Categories: Unit
 * Coverage Target: 100%
 *
 * Tests cache storage and retrieval, cache clearing, performance testing,
 * and cache consistency for the WalletConfig helper.
 *
 * Critical for wallet system configuration performance.
 *
 * @group parallel-safe
 * @group wallet-config
 */
class WalletConfigCachingTest extends TestCase
{
    protected function setUp(): void
    {
        parent::setUp();

        // Mock wallet configurations
        config([
            'wallet.currencies' => [
                'EUR' => [
                    'code' => 'EUR',
                    'name' => 'Euro',
                    'symbol' => '€',
                    'is_default' => true,
                    'enabled' => true,
                ],
                'USD' => [
                    'code' => 'USD',
                    'name' => 'US Dollar',
                    'symbol' => '$',
                    'is_default' => false,
                    'enabled' => true,
                ],
            ],
            'wallet.rules' => [
                'deposit' => [
                    'enabled' => true,
                    'min_amount' => 10.00,
                    'max_amount' => 10000.00,
                ],
                'withdrawal' => [
                    'enabled' => true,
                    'min_amount' => 5.00,
                    'max_amount' => 5000.00,
                ],
            ],
        ]);
    }

    protected function tearDown(): void
    {
        WalletConfig::clearCache();
        parent::tearDown();
    }

    /** @test */
    public function it_should_cache_configuration_values(): void
    {
        // Act - First call should load from config
        $result1 = WalletConfig::get('currencies.EUR.name');

        // Modify the underlying config
        config(['wallet.currencies.EUR.name' => 'Modified Euro']);

        // Act - Second call should return cached value
        $result2 = WalletConfig::get('currencies.EUR.name');

        // Assert
        $this->assertEquals('Euro', $result1);
        $this->assertEquals('Euro', $result2); // Should still be cached value
    }

    /** @test */
    public function it_should_clear_cache_and_reload_configuration(): void
    {
        // Arrange - Load initial value into cache
        $initialResult = WalletConfig::get('currencies.EUR.name');
        $this->assertEquals('Euro', $initialResult);

        // Modify config
        config(['wallet.currencies.EUR.name' => 'Modified Euro']);

        // Act - Clear cache
        WalletConfig::clearCache();

        // Get value after cache clear
        $newResult = WalletConfig::get('currencies.EUR.name');

        // Assert
        $this->assertEquals('Modified Euro', $newResult);
    }

    /** @test */
    public function it_should_cache_entire_config_files(): void
    {
        // Act - First call should load entire currencies config
        $currencies1 = WalletConfig::currencies();

        // Modify the underlying config
        config(['wallet.currencies.GBP' => [
            'code' => 'GBP',
            'name' => 'British Pound',
            'is_default' => false,
        ]]);

        // Act - Second call should return cached value
        $currencies2 = WalletConfig::currencies();

        // Assert
        $this->assertEquals($currencies1, $currencies2);
        $this->assertArrayNotHasKey('GBP', $currencies2); // Should not have new currency
    }

    /** @test */
    public function it_should_cache_different_config_files_separately(): void
    {
        // Act - Load different config files
        $currencies = WalletConfig::get('currencies');
        $rules = WalletConfig::get('rules');

        // Modify one config file
        config(['wallet.currencies.EUR.name' => 'Modified Euro']);

        // Clear cache
        WalletConfig::clearCache();

        // Reload one config file
        $newCurrencies = WalletConfig::get('currencies');
        $cachedRules = WalletConfig::get('rules');

        // Assert
        $this->assertEquals('Modified Euro', $newCurrencies['EUR']['name']);
        $this->assertEquals($rules, $cachedRules); // Rules should be reloaded but same
    }

    /** @test */
    public function it_should_cache_dot_notation_access(): void
    {
        // Act - Access nested value with dot notation
        $result1 = WalletConfig::get('currencies.EUR.name');

        // Modify config
        config(['wallet.currencies.EUR.name' => 'Modified Euro']);

        // Act - Access same nested value again
        $result2 = WalletConfig::get('currencies.EUR.name');

        // Assert
        $this->assertEquals('Euro', $result1);
        $this->assertEquals('Euro', $result2); // Should be cached
    }

    /** @test */
    public function it_should_cache_default_values(): void
    {
        // Act - Access non-existent key with default value
        $result1 = WalletConfig::get('currencies.INVALID.name', 'default_value');

        // Modify config to add the key
        config(['wallet.currencies.INVALID.name' => 'New Value']);

        // Act - Access same key again
        $result2 = WalletConfig::get('currencies.INVALID.name', 'default_value');

        // Assert
        $this->assertEquals('default_value', $result1);
        $this->assertEquals('default_value', $result2); // Should return cached default
    }

    /** @test */
    public function it_should_handle_cache_for_helper_methods(): void
    {
        // Act - Use helper methods that internally use get()
        $defaultCurrency1 = WalletConfig::defaultCurrency();
        $enabledCurrencies1 = WalletConfig::enabledCurrencies();

        // Modify config
        config([
            'wallet.currencies.EUR.is_default' => false,
            'wallet.currencies.USD.is_default' => true,
            'wallet.currencies.EUR.enabled' => false,
        ]);

        // Act - Call helper methods again
        $defaultCurrency2 = WalletConfig::defaultCurrency();
        $enabledCurrencies2 = WalletConfig::enabledCurrencies();

        // Assert - Should return cached values
        $this->assertEquals($defaultCurrency1, $defaultCurrency2);
        $this->assertEquals($enabledCurrencies1, $enabledCurrencies2);
        $this->assertEquals('EUR', $defaultCurrency2['code']); // Still cached EUR as default
        $this->assertArrayHasKey('EUR', $enabledCurrencies2); // Still cached EUR as enabled
    }

    /** @test */
    public function it_should_clear_cache_for_all_config_files(): void
    {
        // Arrange - Load multiple config files into cache
        WalletConfig::get('currencies');
        WalletConfig::get('rules');

        // Modify multiple config files
        config([
            'wallet.currencies.EUR.name' => 'Modified Euro',
            'wallet.rules.deposit.min_amount' => 20.00,
        ]);

        // Act - Clear cache
        WalletConfig::clearCache();

        // Get values after cache clear
        $newEuroName = WalletConfig::get('currencies.EUR.name');
        $newMinAmount = WalletConfig::get('rules.deposit.min_amount');

        // Assert
        $this->assertEquals('Modified Euro', $newEuroName);
        $this->assertEquals(20.00, $newMinAmount);
    }

    /** @test */
    public function it_should_handle_cache_consistency_across_method_calls(): void
    {
        // Act - Call different methods that access same config
        $currencies = WalletConfig::currencies();
        $eurCurrency = WalletConfig::currency('EUR');
        $isEurEnabled = WalletConfig::isCurrencyEnabled('EUR');
        $isEurDefault = WalletConfig::isCurrencyDefault('EUR');

        // Assert - All should be consistent
        $this->assertEquals($currencies['EUR'], $eurCurrency);
        $this->assertEquals($currencies['EUR']['enabled'], $isEurEnabled);
        $this->assertEquals($currencies['EUR']['is_default'], $isEurDefault);
    }

    /** @test */
    public function it_should_handle_cache_for_business_rules(): void
    {
        // Act - Load business rules
        $depositRules1 = WalletConfig::operationRules('deposit');
        $isDepositEnabled1 = WalletConfig::isOperationEnabled('deposit');

        // Modify config
        config([
            'wallet.rules.deposit.enabled' => false,
            'wallet.rules.deposit.min_amount' => 50.00,
        ]);

        // Act - Access rules again
        $depositRules2 = WalletConfig::operationRules('deposit');
        $isDepositEnabled2 = WalletConfig::isOperationEnabled('deposit');

        // Assert - Should return cached values
        $this->assertEquals($depositRules1, $depositRules2);
        $this->assertEquals($isDepositEnabled1, $isDepositEnabled2);
        $this->assertTrue($isDepositEnabled2); // Still cached as enabled
        $this->assertEquals(10.00, $depositRules2['min_amount']); // Still cached original value
    }

    /** @test */
    public function it_should_handle_cache_for_validation(): void
    {
        // Act - Run validation (which accesses multiple configs)
        $errors1 = WalletConfig::validate();

        // Introduce validation errors
        config([
            'wallet.currencies.EUR.is_default' => false,
            'wallet.currencies.USD.is_default' => false,
        ]);

        // Act - Run validation again
        $errors2 = WalletConfig::validate();

        // Assert - Should return cached validation result
        $this->assertEquals($errors1, $errors2);
        $this->assertEmpty($errors2); // Should still be empty from cache
    }

    /** @test */
    public function it_should_handle_performance_with_repeated_access(): void
    {
        // Arrange
        $iterations = 100;
        $startTime = microtime(true);

        // Act - Repeated access to same config value
        for ($i = 0; $i < $iterations; $i++) {
            WalletConfig::get('currencies.EUR.name');
        }

        $endTime = microtime(true);
        $duration = $endTime - $startTime;

        // Assert - Should be fast due to caching (less than 10ms for 100 iterations)
        $this->assertLessThan(0.01, $duration, 'Cached access should be very fast');
    }

    /** @test */
    public function it_should_handle_cache_isolation_between_tests(): void
    {
        // This test verifies that tearDown() properly clears cache

        // Act - Set a value and verify it's cached
        $result1 = WalletConfig::get('currencies.EUR.name');
        $this->assertEquals('Euro', $result1);

        // Modify config
        config(['wallet.currencies.EUR.name' => 'Test Euro']);

        // Verify it's still cached
        $result2 = WalletConfig::get('currencies.EUR.name');
        $this->assertEquals('Euro', $result2);

        // Note: tearDown() will clear cache, so next test should start fresh
    }

    /** @test */
    public function it_should_handle_cache_with_null_values(): void
    {
        // Arrange
        config(['wallet.currencies.EUR.nullable_field' => null]);

        // Act - Access null value with default
        $result1 = WalletConfig::get('currencies.EUR.nullable_field', 'default');

        // Modify config
        config(['wallet.currencies.EUR.nullable_field' => 'new_value']);

        // Act - Access again
        $result2 = WalletConfig::get('currencies.EUR.nullable_field', 'default');

        // Assert - Should return cached null value
        $this->assertNull($result1);
        $this->assertNull($result2);
    }

    /** @test */
    public function it_should_handle_cache_with_empty_arrays(): void
    {
        // Arrange - Use valid config file
        config(['wallet.currencies' => []]);
        WalletConfig::clearCache();

        // Act - Access empty array
        $result1 = WalletConfig::get('currencies');

        // Modify config
        config(['wallet.currencies' => ['EUR' => ['code' => 'EUR', 'name' => 'Euro']]]);

        // Act - Access again
        $result2 = WalletConfig::get('currencies');

        // Assert - Should return cached empty array
        $this->assertIsArray($result1);
        $this->assertEmpty($result1);
        $this->assertEquals($result1, $result2);
    }

    /** @test */
    public function it_should_handle_cache_memory_efficiency(): void
    {
        // Act - Load multiple large config sections
        $currencies = WalletConfig::get('currencies');
        $rules = WalletConfig::get('rules');

        // Access same data multiple times
        for ($i = 0; $i < 10; $i++) {
            WalletConfig::get('currencies');
            WalletConfig::get('rules');
            WalletConfig::currencies();
            WalletConfig::operationRules('deposit');
        }

        // Assert - Memory usage should be reasonable
        $memoryUsage = memory_get_usage();
        $this->assertLessThan(75 * 1024 * 1024, $memoryUsage, 'Memory usage should be reasonable'); // Less than 50MB
    }
}
